import { clearStoredToken, getStoredToken } from "../api/axiosInstance"
import { getMyProfile, refreshToken as refreshTokenApi } from "src/api"
import { profileAtom } from "../context/AuthContext"
import { useAtom } from "jotai"

export const useLogin = () => {
  const [, setProfile] = useAtom(profileAtom)

  const getProfile = async (address: string) => {
    const userProfile = await getMyProfile(address)
    setProfile(userProfile)
  }

  const prepare = async (needRefresh: boolean = false) => {
    const { address, refreshToken, refreshTokenExpiredTimeInSeconds } =
      await getStoredToken()

    const canRefreshToken =
      refreshToken &&
      refreshTokenExpiredTimeInSeconds &&
      Number(refreshTokenExpiredTimeInSeconds) * 1000 > Date.now()
    if (!address) {
      await clearStoredToken()
      return
    }
    if (needRefresh && !canRefreshToken) {
      await clearStoredToken()
      return
    } else if (needRefresh) {
      await refreshTokenApi(refreshToken!)
    }
    await getProfile(address)
  }

  return { prepare }
}
