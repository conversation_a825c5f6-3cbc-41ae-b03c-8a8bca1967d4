import React, { createContext, useEffect } from "react"
import { Alert } from "react-native"
import { atom, useAtom } from "jotai"
import {
  useAccount,
  useAccountEffect,
  useChainId,
  useDisconnect,
  useSignMessage,
  useSwitchChain,
} from "wagmi"
import {
  clearStoredToken,
  setupResponseInterceptor,
} from "src/api/axiosInstance"
import { getMyProfile, getNonce, login, SignInResponse, User } from "src/api"
import { PROVIDER_CHAIN_ID } from "src/config/env"
import { useAppKit } from "@reown/appkit-wagmi-react-native"
import { appChains } from "src/provider/walletconnect/WalletConnectProvider"
import { WELCOME_MESSAGE } from "src/siweConfig"
import { retry } from "utils/promise"
import { useHandleError } from "src/api/errors/handleError"
import { ConnectionUtil } from "@reown/appkit-core-react-native"

// ----------------------------
// Interfaces & Constants
// ----------------------------

interface AuthContextType {
  isAuthenticated: boolean
  connectWallet: () => Promise<void>
  logout: (needDisconnectWallet: boolean) => Promise<void>
}

// Atom lưu trữ profile người dùng
export const profileAtom = atom<User | null>(null)

// Tạo context với default value
export const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  connectWallet: async () => {},
  logout: async () => {},
})

// ----------------------------
// AuthProvider Component
// ----------------------------

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const { address, isConnected } = useAccount()
  const [profile, setProfile] = useAtom(profileAtom)
  const { open, close } = useAppKit()
  const { disconnectAsync } = useDisconnect()
  const chainId = useChainId()
  const { switchChainAsync } = useSwitchChain()
  const { signMessageAsync } = useSignMessage()
  const { handleError } = useHandleError()

  const isAuthenticated = !!profile && !!address

  useEffect(() => {
    if (isAuthenticated) {
      if (chainId !== PROVIDER_CHAIN_ID) {
        handleSwitchChain(PROVIDER_CHAIN_ID)
      }
    }
  }, [profile, address, chainId])

  const handleSwitchChain = async (chainId: number) => {
    try {
      const result = await switchChainAsync({ chainId })
      if (result.id !== chainId) {
        throw new Error("Network switch failed")
      }
      return true
    } catch {
      return false
    }
  }

  const onLogin = async (address: string, isConnecting = false) => {
    try {
      if (profile || !isConnecting) return
      const chain = appChains.find((chain) => chain.id === chainId)
      if (address && !chain) {
        await handleSwitchChain(PROVIDER_CHAIN_ID)
      }
      const nonce = await getNonce(address)
      const signature = await signMessageAsync({
        message: WELCOME_MESSAGE(address, nonce),
      })
      await retry<SignInResponse>(() => login(address, nonce, signature))
      const userProfile = await retry<User>(() => getMyProfile(address))
      setProfile(userProfile!)
    } catch (error) {
      handleError(error)
    }
  }

  const onConnectWallet = async () => {
    if (address && isConnected) {
      await onLogin(address, true)
    } else {
      await disconnectAsync()
      await ConnectionUtil.disconnect()
      await open()
    }
  }

  const onLogout = async (needDisconnectWallet: boolean = false) => {
    if (needDisconnectWallet) {
      await close()
      await disconnectAsync()
      await ConnectionUtil.disconnect()
    }
    clearStoredToken()
    setProfile(null)
  }

  const handleConnect = async (address: string, chainId: number) => {
    const chain = appChains.find((chain) => chain.id === chainId)
    if (address && !chain) {
      await handleSwitchChain(PROVIDER_CHAIN_ID)
    }
    // else {
    //   onLogin(address, isConnecting)
    // }
  }

  useAccountEffect({
    onConnect(data) {
      handleConnect(data.address, data.chainId)
    },
    onDisconnect() {
      clearStoredToken()
      setProfile(null)
    },
  })

  // Setup interceptor cho token hết hạn
  useEffect(() => {
    setupResponseInterceptor(() => {
      Alert.alert("Session Expired", "Please login again")
      onLogout()
    })
  }, [onLogout])

  return (
    <AuthContext.Provider
      value={{
        logout: onLogout,
        connectWallet: onConnectWallet,
        isAuthenticated,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}
