export const PROVIDER_CHAIN_ID = parseInt(
  (process.env.EXPO_PUBLIC_PROVIDER_CHAINID ?? 0).toString()
)

export const BASE_WEB_URL = process.env.EXPO_PUBLIC_BASE_WEB_URL || ""

export const APP_SCHEME = process.env.EXPO_PUBLIC_REDIRECT_URL || ""
export const WALLET_CONNECT_PROJECT_ID =
  process.env.EXPO_PUBLIC_WALLET_CONNECT_PROJECT_ID || ""

export const PROVIDER_POLLING_INTERVAL = parseInt(
  (process.env.EXPO_PUBLIC_PROVIDER_POLLING_INTERVAL ?? 5000).toString()
)
export const MAX_UINT256 =
  "115792089237316195423570985008687907853269984665640564039457584007913129639935"

export const EXPO_PUBLIC_BASE_API_URL = process.env.EXPO_PUBLIC_BASE_API_URL
export const EXPO_PUBLIC_BASE_IMAGE_URL = process.env.EXPO_PUBLIC_BASE_IMAGE_URL
export const BSCSCAN_URL = process.env.EXPO_PUBLIC_BSCSCAN_URL

export const CONTRACT_ADDRESS_LIQUIDATION_CURRENCY = process.env
  .EXPO_PUBLIC_LIQUIDATION_CURRENCY_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_PRIMARY_TOKEN = process.env
  .EXPO_PUBLIC_PRIMARY_TOKEN_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_TREASURY = process.env
  .EXPO_PUBLIC_TREASURY_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_STAKE_TOKEN_1 = process.env
  .EXPO_PUBLIC_STAKE_TOKEN_1_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_STAKE_TOKEN_2 = process.env
  .EXPO_PUBLIC_STAKE_TOKEN_2_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_STAKE_TOKEN_3 = process.env
  .EXPO_PUBLIC_STAKE_TOKEN_3_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_BACKER_ROUND_DISTRIBUTOR = process.env
  .EXPO_PUBLIC_BACKER_ROUND_DISTRIBUTOR_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_SEED_ROUND_DISTRIBUTOR = process.env
  .EXPO_PUBLIC_SEED_ROUND_DISTRIBUTOR_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_PRIVATE_SALE_1_DISTRIBUTOR = process.env
  .EXPO_PUBLIC_PRIVATE_SALE_1_DISTRIBUTOR_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_PRIVATE_SALE_2_DISTRIBUTOR = process.env
  .EXPO_PUBLIC_PRIVATE_SALE_2_DISTRIBUTOR_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_CORE_TEAM_DISTRIBUTOR = process.env
  .EXPO_PUBLIC_CORE_TEAM_DISTRIBUTOR_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_PUBLIC_SALE_AUCTION = process.env
  .EXPO_PUBLIC_PUBLIC_SALE_AUCTION_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_ESTATE_TOKEN = process.env
  .EXPO_PUBLIC_ESTATE_TOKEN_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_ESTATE_FORGER = process.env
  .EXPO_PUBLIC_ESTATE_FORGER_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_ESTATE_MARKETPLACE = process.env
  .EXPO_PUBLIC_ESTATE_MARKETPLACE_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_COMMISSION_TOKEN = process.env
  .EXPO_PUBLIC_COMMISSION_TOKEN_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_COMMISSION_MARKETPLACE = process.env
  .EXPO_PUBLIC_COMMISSION_MARKETPLACE_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_MORTGAGE_TOKEN = process.env
  .EXPO_PUBLIC_MORTGAGE_TOKEN_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE = process.env
  .EXPO_PUBLIC_MORTGAGE_MARKETPLACE_ADDRESS as `0x${string}`
