/**
 * Constants for React Query keys used throughout the application
 * Helps avoid hardcoding and maintains consistency
 */
export const QueryKeys = {
  // Estate related queries
  ESTATE: {
    DETAIL: (id: string) => ["getEstateDetailById", id],
    BALANCES: (id: string) => ["getEstateBalances", id],
    REQUEST_DETAIL: (id: string) => ["getEstateRequestDetailById", id],
    REQUEST_DEPOSITORS: (id: string) => ["getDepositorsByEstateRequestId", id],
    DEPOSITORS: (id: string) => ["getDepositorsByEstateRequestId", id],
    LIST: ["getEstates"],
    REQUESTS: ["getEstateRequests"],
    DEPOSITS: (id: string) => ["getDepositListByRequestId", id],
    ACTIVITIES: (id: string) => ["getEstateActivities", id],
    LEGAL_REQUIREMENTS: (id: string) => ["getLegalRequirements", id],
    HOME_LEADERBOARD: ["getHomeLeaderboard"],
  },

  // Marketplace related queries
  MARKETPLACE: {
    OFFERS: (estateId?: string) => ["getMarketPlaceOffers", estateId],
    ORDERS: ["getMarketplaceOrders"],
    MY_ORDERS: ["getMyMarketplaceOrders"],
    CANCEL_ORDER: ["cancelMarketplaceOrder"],
    HOME_RECENT_OFFERS: ["getHomeRecentOffers"],
  },

  // Currency related queries
  CURRENCY: {
    LIST: ["getCurrencies"],
    BALANCE: (address: string) => ["getCurrencyBalance", address],
  },

  // Mortgage related queries
  MORTGAGE: {
    LOANS: (estateId?: string) => ["getMortgageLoans", estateId],
    MY_LOANS: ["getMyMortgageLoans"],
    LOAN_DETAIL: (id: string) => ["getMortgageLoanDetail", id],
    CREATE_LOAN: ["createMortgageLoan"],
    REPAY_LOAN: ["repayMortgageLoan"],
    FORECLOSE_LOAN: ["forecloseMortgageLoan"],
  },

  // Profile related queries
  PROFILE: {
    DETAIL: ["getProfileDetail"],
    UPDATE: ["updateProfile"],
    MY_PROFILE: ["getMyProfile"],
    VERIFY: ["verifyProfile"],
    APPLICATIONS: ["getApplications"],
    APPLICATION_DETAIL: (id: string) => ["getApplicationDetailById", id],
    MY_TOKENIZATIONS: ["getMyTokenizations"],
    MY_ESTATES: ["getMyEstates"],
    MY_OFFERS: (seller?: string) => ["getMyMarketPlaceOffers", seller],
    MY_LOANS: (involvedAccount?: string) => [
      "getMyMortgageLoans",
      involvedAccount,
    ],
    MY_ACTIVITIES: (involvedAccount?: string) => [
      "getMyActivities",
      involvedAccount,
    ],
  },

  // Authentication related queries
  AUTH: {
    LOGIN: ["login"],
    LOGOUT: ["logout"],
    VERIFY: ["verify"],
    NONCE: ["getNonce"],
  },

  // Wallet related queries
  WALLET: {
    BALANCE: (address: string) => ["getWalletBalance", address],
    TRANSACTIONS: (address: string) => ["getWalletTransactions", address],
    APPROVE: ["approve"],
    IS_APPROVED: (address: string, spender: string) => [
      "isApproved",
      address,
      spender,
    ],
  },

  // Auction related queries
  AUCTION: {
    LIST: ["getAuctions"],
    DETAIL: (id: string) => ["getAuctionDetail", id],
    MY_BIDS: ["getMyAuctionBids"],
    PLACE_BID: ["placeAuctionBid"],
  },

  // Staking related queries
  STAKING: {
    POOLS: ["getStakingPools"],
    MY_STAKES: ["getMyStakes"],
    REWARDS: ["getStakingRewards"],
    STAKE: ["stake"],
    UNSTAKE: ["unstake"],
    CLAIM_REWARDS: ["claimRewards"],
  },

  ORACLE: {
    LIST: ["getOracles"],
  },

  // Treasury related queries
  TREASURY: {
    STATS: ["getTreasuryStats"],
    TRANSACTIONS: ["getTreasuryTransactions"],
    ALLOCATIONS: ["getTreasuryAllocations"],
  },

  // Whitelist related queries
  WHITELIST: {
    CHECK: (address: string) => ["checkWhitelist", address],
    REQUEST: ["requestWhitelist"],
  },

  // Referral related queries
  REFERRAL: {
    LIST: ["myReferrals"],
  },
} as const

export default QueryKeys
