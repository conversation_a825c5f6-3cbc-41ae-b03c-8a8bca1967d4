import { useSuspenseQuery } from "@tanstack/react-query"
import { getCurrencies } from "src/api"
import { QueryKeys } from "src/config/queryKeys"
import { queryOptions } from "@tanstack/react-query"
import { Currency } from "src/api/types/currency"

const getCurrenciesQueryOptions = queryOptions<Currency[]>({
  queryKey: [QueryKeys.CURRENCY.LIST],
  queryFn: () => getCurrencies(),
})

export function useCurrencies(currency: string) {
  const { data: currencies = [] } = useSuspenseQuery(getCurrenciesQueryOptions)

  const tokenData = currency
    ? currencies.find((c: Currency) => c.currency === currency)
    : undefined

  const tokenSymbol = tokenData?.symbol || ""
  const tokenImageUrl = tokenData?.imageUrl || ""
  const rateValue = tokenData?.rate?.value || "0"
  const rateUpdatedAt = tokenData?.rate?.updatedAt || "0"
  const tokenDecimals = tokenData?.rate?.decimals || 0
  const isShowChainlinkPriceFeed =
    rateValue !== "0" && Number(rateUpdatedAt) > 0

  return {
    tokenSymbol,
    tokenImageUrl,
    rateValue,
    rateUpdatedAt,
    tokenDecimals,
    isShowChainlinkPriceFeed,
  }
}
