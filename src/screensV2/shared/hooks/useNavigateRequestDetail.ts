import { TokenizationRequest } from "src/api/types"
import {
  Navigation<PERSON>rop,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import * as Routes from "src/navigatorV2/routes/RoutesV2"

interface UseNavigateRequestDetailProps {
  tokenizationRequest: TokenizationRequest
}

type NavigationParams = {
  [Routes.ESTATE_DETAIL]: { estateId: string }
  [Routes.ESTATE_REQUEST_DETAIL]: { estateRequestId: string }
}

export function useNavigateRequestDetail({
  tokenizationRequest,
}: UseNavigateRequestDetailProps) {
  const navigation =
    useNavigation<NavigationProp<ParamListBase, keyof NavigationParams>>()

  function handleNavigate() {
    const { estateId, id: estateRequestId } = tokenizationRequest

    const isEstate = Boolean(estateId) && estateId !== "0"
    const isEstateRequest = Boolean(estateRequestId) && estateRequestId !== "0"

    if (isEstate) {
      navigation.navigate(Routes.ESTATE_DETAIL, { estateId })
      return
    }

    if (isEstateRequest) {
      navigation.navigate(Routes.ESTATE_REQUEST_DETAIL, {
        estateRequestId,
      })
      return
    }
  }

  return handleNavigate
}
