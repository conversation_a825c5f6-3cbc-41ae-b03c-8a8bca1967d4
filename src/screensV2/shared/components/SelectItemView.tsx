import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { CustomPressable } from "src/componentsv2"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import icNext from "assets/imagesV2/ic_next.png"

interface SelectItemViewProps {
  clickable?: boolean
  icon: React.ReactNode
  title: string
  note?: string
  showNextIcon?: boolean
  onPress: () => void
}

const SelectItemView: React.FC<SelectItemViewProps> = ({
  clickable = true,
  icon,
  title,
  note,
  showNextIcon = true,
  onPress,
}) => {
  return (
    <CustomPressable onPress={onPress} enabled={clickable}>
      <View style={styles.settingItem}>
        <View style={styles.row}>
          {icon}
          <Text style={styles.title}>{title}</Text>
        </View>
        <View style={styles.row}>
          {note && <Text style={styles.note}>{note}</Text>}
          {showNextIcon && (
            <Image source={icNext} style={viewStyles.size18Icon} />
          )}
        </View>
      </View>
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingItem: {
    justifyContent: "space-between",
    flexDirection: "row",
    paddingVertical: 12,
  },
  title: {
    ...textStyles.LMedium,
    marginStart: 12,
    color: Colors.Neutral300,
  },
  note: {
    ...textStyles.LRegular,
    color: Colors.Neutral700,
    marginEnd: 4,
  },
})

export default SelectItemView
