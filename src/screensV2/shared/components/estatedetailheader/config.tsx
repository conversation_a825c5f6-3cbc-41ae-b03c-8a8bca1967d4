import React from "react"
import { Image, View } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import iconConfirmed from "assets/imagesV2/ic_confirmed.png"
import iconPending from "assets/imagesV2/ic_pending.png"
import iconSquareUser from "assets/imagesV2/ic_square_user.png"

interface EstateStatusConfig {
  label: string
  textColor: string
  backgroundColor: string
  borderColor: string
  icon: React.ReactNode
}

const useEstateStatusConfig = (status: string): EstateStatusConfig => {
  const { t } = useTranslation()
  const configs: Record<string, EstateStatusConfig> = {
    // Step 1 statuses
    APPLICATION_VALIDATING: {
      label: t("Pending"),
      textColor: Colors.PalleteBlack,
      backgroundColor: Colors.PalleteWhite,
      borderColor: Colors.PalleteWhite,
      icon: (
        <Image
          style={{
            width: 14,
            height: 14,
          }}
          source={iconPending}
        />
      ),
    },
    APPLICATION_CANCELLED: {
      label: t("Cancelled"),
      textColor: Colors.white,
      backgroundColor: Colors.Danger800,
      borderColor: Colors.Danger900,
      icon: (
        <View
          style={{
            width: 10,
            height: 10,
            backgroundColor: Colors.Danger500,
            borderRadius: 5,
          }}
        />
      ),
    },
    // Step 2 statuses
    SELLING: {
      label: t("Public sale"),
      textColor: Colors.white,
      backgroundColor: Colors.Success900,
      borderColor: Colors.Success800,
      icon: (
        <View
          style={{
            width: 10,
            height: 10,
            backgroundColor: Colors.Success500,
            borderRadius: 5,
          }}
        />
      ),
    },
    REQUEST_SELLING: {
      label: t("Public sale"),
      textColor: Colors.white,
      backgroundColor: Colors.Success900,
      borderColor: Colors.Success800,
      icon: (
        <View
          style={{
            width: 10,
            height: 10,
            backgroundColor: Colors.Success500,
            borderRadius: 5,
          }}
        />
      ),
    },
    REQUEST_INSUFFICIENT_SOLD_AMOUNT: {
      label: t("Cancelled"),
      textColor: Colors.white,
      backgroundColor: Colors.Danger800,
      borderColor: Colors.Danger900,
      icon: (
        <View
          style={{
            width: 10,
            height: 10,
            backgroundColor: Colors.Danger500,
            borderRadius: 5,
          }}
        />
      ),
    },
    // Step 3 statuses
    REQUEST_TRANSFERRING_OWNERSHIP: {
      label: t("Transferring ownership"),
      textColor: Colors.white,
      backgroundColor: Colors.Warning900,
      borderColor: Colors.Warning800,
      icon: (
        <Image
          style={{
            width: 14,
            height: 14,
          }}
          source={iconSquareUser}
        />
      ),
    },
    REQUEST_EXPIRED: {
      label: t("Expired"),
      textColor: Colors.white,
      backgroundColor: Colors.Danger800,
      borderColor: Colors.Danger900,
      icon: (
        <View
          style={{
            width: 10,
            height: 10,
            backgroundColor: Colors.Danger500,
            borderRadius: 5,
          }}
        />
      ),
    },
    REQUEST_CANCELLED: {
      label: t("Cancelled"),
      textColor: Colors.white,
      backgroundColor: Colors.Danger800,
      borderColor: Colors.Danger900,
      icon: (
        <View
          style={{
            width: 10,
            height: 10,
            backgroundColor: Colors.Danger500,
            borderRadius: 5,
          }}
        />
      ),
    },
    // Step 4 status
    REQUEST_CONFIRMED: {
      label: t("Tokenized"),
      textColor: Colors.white,
      backgroundColor: Colors.Success600,
      borderColor: Colors.Success300,
      icon: (
        <Image
          style={{
            width: 14,
            height: 14,
          }}
          source={iconConfirmed}
        />
      ),
    },
  }
  return configs[status]
}

export { useEstateStatusConfig }
