import React, { useState } from "react"
import { View, Text, StyleSheet, Image, Pressable } from "react-native"
import { useTranslation } from "react-i18next"
import { useNavigation } from "@react-navigation/native"
import { ExpandView } from "src/componentsv2"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import iconBack from "assets/imagesV2/ic_back.png"
import { useEstateStatusConfig } from "./config"
import StatusBottomSheet from "./StatusBottomSheet"

const EstateStatus: React.FC<{ status: string }> = ({ status }) => {
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false)
  const config = useEstateStatusConfig(status)

  const handlePress = () => {
    setIsBottomSheetVisible(true)
  }

  return (
    <>
      <Pressable
        onPress={handlePress}
        style={{
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor,
          borderWidth: 1,
          borderRadius: 13,
          paddingHorizontal: 12,
          height: 26,
          alignItems: "center",
          flexDirection: "row",
        }}
      >
        {config.icon}
        <Text
          style={[
            textStyles.MSemiBold,
            { color: config.textColor, marginStart: 6 },
          ]}
        >
          {config.label}
        </Text>
      </Pressable>

      <StatusBottomSheet
        visible={isBottomSheetVisible}
        onClose={() => setIsBottomSheetVisible(false)}
        currentStatus={status}
      />
    </>
  )
}

interface EstateDetailHeaderProps {
  status?: string
}

const EstateDetailHeader: React.FC<EstateDetailHeaderProps> = ({ status }) => {
  const { t } = useTranslation()
  const navigation = useNavigation()

  return (
    <View style={styles.container}>
      <Pressable onPress={() => navigation.goBack()}>
        <Image
          source={iconBack}
          style={styles.icon}
          tintColor={Colors.Neutral300}
        />
      </Pressable>
      <Text
        style={[
          textStyles.LMedium,
          { color: Colors.Neutral300, marginStart: 8 },
        ]}
      >
        {t("Details")}
      </Text>
      <ExpandView />
      {status && <EstateStatus status={status} />}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    height: 60,
    paddingHorizontal: 16,
  },
  icon: {
    width: 20,
    height: 20,
  },
})

export default EstateDetailHeader
