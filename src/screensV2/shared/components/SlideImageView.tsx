import React, { useState, useRef } from "react"
import {
  View,
  Image,
  FlatList,
  Dimensions,
  StyleSheet,
  ViewToken,
  ViewabilityConfig,
  ViewabilityConfigCallbackPair,
  Text,
} from "react-native"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
interface SlideImageViewProps {
  images: string[]
  height?: number
}

const { width } = Dimensions.get("window")
const SCREEN_WIDTH = width - 32

const SlideImageView: React.FC<SlideImageViewProps> = ({
  images,
  height = 190,
}) => {
  const [activeIndex, setActiveIndex] = useState(0)
  const flatListRef = useRef<FlatList>(null)

  const onViewableItemsChanged = ({
    viewableItems,
  }: {
    viewableItems: ViewToken[]
  }) => {
    if (viewableItems.length > 0) {
      setActiveIndex(viewableItems[0].index || 0)
    }
  }

  const viewabilityConfig: ViewabilityConfig = {
    itemVisiblePercentThreshold: 50,
  }

  const viewabilityConfigCallbackPairs = useRef<
    ViewabilityConfigCallbackPair[]
  >([{ viewabilityConfig, onViewableItemsChanged }])

  const renderItem = ({ item }: { item: string }) => (
    <View style={[styles.imageContainer, { height }]}>
      <Image
        source={{ uri: item }}
        style={styles.image}
        resizeMode="contain"
        fadeDuration={0}
      />
    </View>
  )

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={images}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current}
        keyExtractor={(_, index) => index.toString()}
      />
      {images.length > 0 && (
        <View style={styles.counterContainer}>
          <Text
            style={styles.counterText}
          >{`${activeIndex + 1}/${images.length}`}</Text>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  imageContainer: {
    width: SCREEN_WIDTH,
    overflow: "hidden",
  },
  image: {
    flex: 1,
    borderRadius: 6,
  },
  counterContainer: {
    position: "absolute",
    bottom: 0,
    right: 0,
    margin: 4,
    borderRadius: 4,
    backgroundColor: Colors.PalleteBlack80,
  },
  counterText: {
    ...textStyles.SBold,
    marginVertical: 4,
    marginHorizontal: 8,
  },
})

export default SlideImageView
