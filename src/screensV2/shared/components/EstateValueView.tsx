import React, { useState } from "react"
import { View, Text, StyleSheet } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { TokenizationRequestState } from "src/api"
import { formatNumericByDecimals, formatCurrency } from "src/utils/format"
import { CustomPressable } from "src/componentsv2"
import { ChainlinkPriceFeedPopup } from "./index"
import { useCurrencies } from "../hooks/useCurrencies"

interface EstateValueViewProps {
  unitPrice: string
  totalSupply: string
  soldAmount?: string
  maxSellingAmount: string
  decimals: number
  currency: string
  state: TokenizationRequestState
}

const EstateValueView: React.FC<EstateValueViewProps> = ({
  unitPrice,
  totalSupply,
  soldAmount = "0",
  maxSellingAmount,
  decimals,
  currency,
  state,
}) => {
  const { t } = useTranslation()

  const { tokenSymbol, rateUpdatedAt, isShowChainlinkPriceFeed } =
    useCurrencies(currency)

  const [isPopupPriceFeedVisible, setIsPopupPriceFeedVisible] = useState(false)

  const formattedUnitPrice = formatNumericByDecimals(unitPrice, decimals)

  const displayEstateValue = `${formatCurrency(
    Number(formattedUnitPrice) * Number(totalSupply)
  )} ${tokenSymbol}`

  const availableAmount = Number(maxSellingAmount) - Number(soldAmount)
  const displayAvailableNFTs =
    state !== TokenizationRequestState.CONFIRMED
      ? `${t("Available")} ${formatCurrency(availableAmount)}/${formatCurrency(maxSellingAmount)} ${t("NFTs")} ${t("at price")} ${formatCurrency(formattedUnitPrice)} ${tokenSymbol}`
      : `${formatCurrency(formattedUnitPrice)} ${tokenSymbol} ${t("per")} ${t("NFT")}`

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t("Estate Value")}</Text>
      <View style={styles.valueContainer}>
        <Text style={styles.value}>{displayEstateValue}</Text>

        <View style={styles.priceContainer}>
          <CustomPressable
            onPress={() => setIsPopupPriceFeedVisible(true)}
            enabled={isShowChainlinkPriceFeed}
          >
            <Text style={styles.availableText}>{displayAvailableNFTs}</Text>
            <View style={styles.dashedUnderline} />
          </CustomPressable>

          {isPopupPriceFeedVisible && (
            <ChainlinkPriceFeedPopup
              isVisible={isPopupPriceFeedVisible}
              onClose={() => setIsPopupPriceFeedVisible(false)}
              chainlinkTimestamp={rateUpdatedAt}
            />
          )}
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.Neutral950,
    gap: 12,
  },
  title: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  valueContainer: {
    gap: 6,
  },
  value: {
    ...textStyles.size3XLMedium,
    color: Colors.PalleteWhite,
  },
  priceContainer: {
    position: "relative",
    alignSelf: "flex-start",
  },
  availableText: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  dashedUnderline: {
    borderWidth: 0.3,
    borderStyle: "dashed",
    borderColor: Colors.Neutral300,
    marginTop: 2,
  },
})

export default EstateValueView
