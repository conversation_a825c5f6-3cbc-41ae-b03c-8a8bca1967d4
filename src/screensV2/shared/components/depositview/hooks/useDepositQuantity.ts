import { useState } from "react"
import { formatNumericByDecimals } from "src/utils/format"

interface UseDepositQuantityProps {
  unitPrice: string
  maxSellingAmount: string
  soldAmount: string
  decimals: number
}

export function useDepositQuantity({
  unitPrice,
  maxSellingAmount,
  soldAmount,
  decimals,
}: UseDepositQuantityProps) {
  const [quantity, setQuantity] = useState(0)

  const formattedUnitPrice = formatNumericByDecimals(unitPrice, decimals)
  const amountToDeposit = quantity * Number(formattedUnitPrice)
  const availableAmount = Number(maxSellingAmount) - Number(soldAmount)

  const handleIncrement = () => {
    setQuantity((prev) => prev + 1)
  }

  const handleDecrement = () => {
    if (quantity > 0) {
      setQuantity((prev) => prev - 1)
    }
  }

  const handleQuantityChange = (text: string) => {
    const numericValue = text.replace(/[^0-9]/g, "")
    if (numericValue === "") {
      setQuantity(0)
    } else if (Number(numericValue) > availableAmount) {
      setQuantity(availableAmount)
    } else {
      setQuantity(parseInt(numericValue))
    }
  }

  return {
    quantity,
    amountToDeposit,
    handleIncrement,
    handleDecrement,
    handleQuantityChange,
    setQuantity,
  }
}
