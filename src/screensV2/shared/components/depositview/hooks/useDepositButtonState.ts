import { TokenizationRequestState } from "src/api/types"
import { DepositButtonType } from "../DepositButton"
import { useContext } from "react"
import { AuthContext } from "src/context/AuthContext"

interface UseDepositButtonStateProps {
  state: TokenizationRequestState
}

export function useDepositButtonState({ state }: UseDepositButtonStateProps) {
  const authContext = useContext(AuthContext)
  const isAuthenticated = authContext.isAuthenticated

  const getButtonState = () => {
    if (state === TokenizationRequestState.SELLING) {
      if (!isAuthenticated) {
        return DepositButtonType.CONNECT
      } else return DepositButtonType.DEPOSIT
    } else if (
      state === TokenizationRequestState.CANCELLED ||
      state === TokenizationRequestState.EXPIRED ||
      state === TokenizationRequestState.INSUFFICIENT_SOLD_AMOUNT ||
      state === TokenizationRequestState.TRANSFERRING_OWNERSHIP ||
      state === TokenizationRequestState.CONFIRMED
    ) {
      return DepositButtonType.ENDED
    }
    return DepositButtonType.CANT_DEPOSIT
  }

  return {
    buttonState: getButtonState(),
  }
}
