import { useAccount } from "wagmi"
import { useWriteContract } from "wagmi"
import { useEthersProvider } from "src/hooks"
import { showError, showSuccessWhenCallContract } from "src/utils/toast"
import { useTranslation } from "react-i18next"
import {
  CONTRACT_ADDRESS_ESTATE_FORGER,
  CONTRACT_ADDRESS_ESTATE_TOKEN,
  MAX_UINT256,
} from "src/config/env"
import { estateForgerAbi } from "src/api/contracts"
import { erc20Abi } from "src/api/contracts/erc20"
import { useErc20Allowance } from "src/api/contracts"
import Logger from "src/utils/logger"
import { useState, useContext } from "react"
import { AuthContext } from "src/context/AuthContext"

const logger = new Logger({ tag: "useDepositActions" })

interface UseDepositActionsProps {
  requestId: string
  currencyId: string
  decimals: number
  quantity: number
  unitPrice: string
  setQuantity: (quantity: number) => void
}

export function useDepositActions({
  requestId,
  currencyId,
  decimals,
  quantity,
  unitPrice,
  setQuantity,
}: UseDepositActionsProps) {
  const { t } = useTranslation()
  const { address } = useAccount()
  const { writeContractAsync } = useWriteContract()
  const ethersProvider = useEthersProvider()
  const [isLoading, setIsLoading] = useState(false)
  const [canCancelLoading, setCanCancelLoading] = useState(true)
  const authContext = useContext(AuthContext)

  const currentCurrencyAllowanceWei = useErc20Allowance(
    address,
    currencyId as `0x${string}`,
    CONTRACT_ADDRESS_ESTATE_TOKEN
  )

  async function onConnectWallet() {
    await authContext.connectWallet()
  }

  async function onDeposit() {
    if (quantity <= 0 || !ethersProvider) return
    try {
      setIsLoading(true)
      const currencyAmountWei =
        BigInt(unitPrice) * BigInt(quantity) * BigInt(Math.pow(10, decimals))
      if (currentCurrencyAllowanceWei < currencyAmountWei) {
        const txHash = await writeContractAsync({
          address: currencyId as `0x${string}`,
          abi: erc20Abi,
          functionName: "approve",
          args: [CONTRACT_ADDRESS_ESTATE_TOKEN, MAX_UINT256],
        })
        setCanCancelLoading(!txHash)
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          throw new Error(t("Approve failed"))
        }
      }
      setCanCancelLoading(true)
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_FORGER,
        abi: estateForgerAbi,
        functionName: "deposit",
        args: [BigInt(requestId), BigInt(quantity)],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Deposit tokenization success") +
            ". " +
            t("Data will be updated in few seconds")
        )
        setIsLoading(false)
        setQuantity(0)
      } else {
        throw new Error(
          t("Deposit tokenization failed " + receipt.transactionHash)
        )
      }
    } catch (e) {
      showError(t("Deposit tokenization failed" + e))
      logger.error("Deposit tokenization failed", e)
    } finally {
      setIsLoading(false)
    }
  }

  return {
    onConnectWallet,
    onDeposit,
    isLoading,
    canCancelLoading,
    setIsLoading,
  }
}
