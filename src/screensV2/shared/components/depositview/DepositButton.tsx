import React from "react"
import { Text, StyleSheet, ViewStyle } from "react-native"
import { CustomPressable } from "src/componentsv2"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"

export enum DepositButtonType {
  CONNECT = "connect",
  ENDED = "ended",
  CANT_DEPOSIT = "cantDeposit",
  DEPOSIT = "deposit",
}

interface DepositButtonProps {
  type: DepositButtonType
  onPress?: () => void
  amount?: number
  tokenSymbol?: string
}

export const DepositButton: React.FC<DepositButtonProps> = ({
  type,
  onPress,
  amount,
  tokenSymbol,
}) => {
  const { t } = useTranslation()

  const getButtonText = () => {
    switch (type) {
      case DepositButtonType.CONNECT:
        return t("Connect Wallet")
      case DepositButtonType.ENDED:
        return t("Sale Ended")
      case DepositButtonType.CANT_DEPOSIT:
        return t("Cant Deposit")
      case DepositButtonType.DEPOSIT:
        return `${t("Deposit")} ${amount} ${tokenSymbol}`
    }
  }

  const getButtonStyle = () => {
    switch (type) {
      case DepositButtonType.ENDED:
        return styles.disabledButton
      case DepositButtonType.CANT_DEPOSIT:
        return styles.disabledButton
      default:
        return styles.enableButton
    }
  }

  return (
    <CustomPressable
      style={getButtonStyle()}
      onPress={onPress}
      enabled={
        type !== DepositButtonType.ENDED &&
        type !== DepositButtonType.CANT_DEPOSIT
      }
    >
      <Text style={styles.buttonText}>{getButtonText()}</Text>
    </CustomPressable>
  )
}

const baseButtonStyle: ViewStyle = {
  height: 38,
  borderRadius: 6,
  flex: 1,
  marginLeft: 12,
  alignItems: "center",
  justifyContent: "center",
}

const styles = StyleSheet.create({
  enableButton: {
    ...baseButtonStyle,
    backgroundColor: Colors.Primary500,
  },
  disabledButton: {
    ...baseButtonStyle,
    backgroundColor: Colors.Neutral700,
  },
  buttonText: {
    ...textStyles.LMedium,
    color: Colors.PalleteBlack,
  },
})
