import React from "react"
import { View, StyleSheet, Image, TextInput } from "react-native"
import { CustomPressable } from "src/componentsv2"
import { SimpleLoadingView } from "src/componentsv2"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import icMinus from "assets/imagesV2/ic_minus.png"
import icPlus from "assets/imagesV2/ic_plus.png"
import { DepositButton, DepositButtonType } from "./DepositButton"
import { TokenizationRequestState } from "src/api/types"
import { useDepositQuantity } from "./hooks/useDepositQuantity"
import { useDepositButtonState } from "./hooks/useDepositButtonState"
import { useDepositActions } from "./hooks/useDepositActions"

interface DepositActionViewProps {
  state: TokenizationRequestState
  tokenSymbol: string
  requestId: string
  currencyId: string
  decimals: number
  unitPrice: string
  maxSellingAmount: string
  soldAmount?: string
}

const DepositActionView: React.FC<DepositActionViewProps> = ({
  state,
  tokenSymbol,
  requestId,
  currencyId,
  decimals,
  unitPrice,
  maxSellingAmount,
  soldAmount = "0",
}) => {
  const {
    quantity,
    amountToDeposit,
    handleIncrement,
    handleDecrement,
    handleQuantityChange,
    setQuantity,
  } = useDepositQuantity({
    unitPrice,
    maxSellingAmount,
    soldAmount,
    decimals,
  })

  const { buttonState } = useDepositButtonState({ state })

  const {
    onConnectWallet,
    onDeposit,
    isLoading,
    canCancelLoading,
    setIsLoading,
  } = useDepositActions({
    requestId,
    currencyId,
    decimals,
    quantity,
    unitPrice,
    setQuantity,
  })

  const isSelling = state === TokenizationRequestState.SELLING

  const renderButton = () => {
    switch (buttonState) {
      case DepositButtonType.CANT_DEPOSIT:
        return <DepositButton type={DepositButtonType.CANT_DEPOSIT} />
      case DepositButtonType.CONNECT:
        return (
          <DepositButton
            type={DepositButtonType.CONNECT}
            onPress={onConnectWallet}
          />
        )
      case DepositButtonType.DEPOSIT:
        return (
          <DepositButton
            type={DepositButtonType.DEPOSIT}
            onPress={onDeposit}
            amount={amountToDeposit}
            tokenSymbol={tokenSymbol}
          />
        )
      case DepositButtonType.ENDED:
        return <DepositButton type={DepositButtonType.ENDED} />
    }
  }

  return (
    <>
      <View style={styles.quantityContainer}>
        <CustomPressable
          style={styles.quantityButton}
          onPress={handleDecrement}
          enabled={isSelling}
        >
          <Image source={icMinus} style={viewStyles.size20Icon} />
        </CustomPressable>

        <TextInput
          style={styles.quantity}
          value={quantity.toString()}
          onChangeText={handleQuantityChange}
          keyboardType="numeric"
          editable={isSelling}
        />

        <CustomPressable
          style={styles.quantityButton}
          onPress={handleIncrement}
          enabled={isSelling}
        >
          <Image source={icPlus} style={viewStyles.size20Icon} />
        </CustomPressable>

        {renderButton()}
      </View>
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

const styles = StyleSheet.create({
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  quantityButton: {
    width: 38,
    height: 38,
    borderRadius: 4,
    borderWidth: 1,
    padding: 16,
    opacity: 0.5,
    backgroundColor: Colors.Neutral900,
    borderColor: Colors.Neutral800,
    alignItems: "center",
    justifyContent: "center",
  },
  quantity: {
    ...textStyles.LMedium,
    width: 64,
    color: Colors.Neutral300,
    textAlign: "center",
    marginHorizontal: 4,
  },
})

export default DepositActionView
