import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { CircularProgress } from "src/componentsv2/CircularProgress"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import clock9Icon from "assets/imagesV2/ic_clock_9.png"
import { useTranslation } from "react-i18next"
import { TokenizationRequestState } from "src/api/types/estate"
import { CustomCountDownTimer, TIME_TYPE } from "src/componentsv2"
import { formatPercentToDisplay } from "src/utils/numberExt"
import { formatCurrency } from "src/utils/format"

interface ProgressSectionViewProps {
  isApplicationDetail?: boolean
  totalSupply: string
  minSellingAmount: string
  maxSellingAmount: string
  soldAmount?: string
  publicSaleEndsAtInSeconds: number
  state: TokenizationRequestState
}

const ProgressSectionView: React.FC<ProgressSectionViewProps> = ({
  isApplicationDetail = false,
  totalSupply,
  minSellingAmount,
  maxSellingAmount,
  soldAmount = "0",
  publicSaleEndsAtInSeconds,
  state,
}) => {
  const { t } = useTranslation()

  const percent = (Number(soldAmount) * 100) / Number(maxSellingAmount)
  const formattedPercent = formatPercentToDisplay(percent)

  const displayTotalSupply = `${formatCurrency(totalSupply)} NFTs`
  const displayMinSellingAmount = `${formatCurrency(minSellingAmount)} NFTs`
  const displayMaxSellingAmount = `${formatCurrency(maxSellingAmount)} NFTs`

  const countDownTime =
    publicSaleEndsAtInSeconds - Math.floor(Date.now() / 1000)
  const isEnded =
    state !== TokenizationRequestState.SELLING ||
    countDownTime <= 0 ||
    soldAmount === maxSellingAmount

  return (
    <View style={styles.container}>
      <View style={styles.progressBoxWrap}>
        <Text style={styles.labelRaise}>{t("Raise Progress")}</Text>
        <View style={styles.progressCircleWrap}>
          <CircularProgress
            percentage={percent}
            size={128}
            strokeWidth={8}
            colorBackground={Colors.Neutral900}
            colorProgress={Colors.PalleteShamrock}
          />
          <View style={styles.progressCenterContent}>
            <Text style={styles.percentText}>{formattedPercent}%</Text>
            {!isApplicationDetail && (
              <View style={styles.timeContainer}>
                <Image source={clock9Icon} style={viewStyles.size12Icon} />
                <CustomCountDownTimer
                  duration={countDownTime}
                  type={TIME_TYPE.TEXT}
                  isEnded={isEnded}
                />
              </View>
            )}
          </View>
        </View>
      </View>

      <View style={styles.infoBoxWrap}>
        <View style={styles.infoBox}>
          <Text style={styles.infoLabel}>{t("Total Supply")}</Text>
          <Text style={styles.infoValue}>{displayTotalSupply}</Text>
        </View>

        <View style={styles.infoBox}>
          <Text style={styles.infoLabel}>{t("Min. Buy")}</Text>
          <Text style={styles.infoValue}>{displayMinSellingAmount}</Text>
        </View>

        <View style={styles.infoBox}>
          <Text style={styles.infoLabel}>{t("Max. Buy")}</Text>
          <Text style={styles.infoValue}>{displayMaxSellingAmount}</Text>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    gap: 12,
    paddingVertical: 16,
  },
  progressBoxWrap: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    padding: 12,
    gap: 16,
  },
  labelRaise: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  progressCircleWrap: {
    height: 130,
    gap: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  progressCenterContent: {
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
  },
  percentText: {
    ...textStyles.size3XLBold,
    color: Colors.Success300,
  },
  timeContainer: {
    flexDirection: "row",
    gap: 4,
  },
  infoBoxWrap: {
    flex: 1,
    gap: 8,
  },
  infoBox: {
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    borderRadius: 6,
    padding: 12,
    gap: 4,
  },
  infoLabel: {
    ...textStyles.MMedium,
    color: Colors.Neutral300,
  },
  infoValue: {
    ...textStyles.XLMedium,
    color: Colors.PalleteWhite,
  },
})

export default ProgressSectionView
