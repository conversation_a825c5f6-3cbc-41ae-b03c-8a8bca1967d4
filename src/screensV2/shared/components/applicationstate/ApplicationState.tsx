import React from "react"
import { Text } from "react-native"
import { ApplicationStatus } from "src/api"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"

// Define status configuration type
interface StatusConfig {
  label: string
  backgroundColor: string
  contentColor: string
}

const useConfig = (status: ApplicationStatus): StatusConfig => {
  const { t } = useTranslation()
  const configs = {
    APPLICATION_VALIDATING: {
      label: t("Pending"),
      backgroundColor: Colors.PalleteWhite,
      contentColor: Colors.PalleteBlack,
    },
    APPLICATION_CANCELLED: {
      label: t("Rejected"),
      backgroundColor: Colors.Danger900,
      contentColor: Colors.Danger500,
    },
    REQUEST_SELLING: {
      label: t("Selling"),
      backgroundColor: "#1FBF6F26",
      contentColor: "#0BF0A7",
    },
    REQUEST_CANCELLED: {
      label: t("Canceled"),
      backgroundColor: Colors.Danger900,
      contentColor: Colors.Danger500,
    },
    REQUEST_EXPIRED: {
      label: t("Expired"),
      backgroundColor: Colors.Danger900,
      contentColor: Colors.Danger500,
    },
    REQUEST_CONFIRMED: {
      label: t("Confirmed"),
      backgroundColor: Colors.Success900,
      contentColor: Colors.Success500,
    },
    REQUEST_INSUFFICIENT_SOLD_AMOUNT: {
      label: t("Canceled"),
      backgroundColor: Colors.Danger900,
      contentColor: Colors.Danger500,
    },
    REQUEST_TRANSFERRING_OWNERSHIP: {
      label: t("Transferring ownership"),
      backgroundColor: Colors.Warning900,
      contentColor: Colors.Warning500,
    },
  }
  return configs[status]
}
interface ApplicationStateProps {
  status: ApplicationStatus
}
const ApplicationState: React.FC<ApplicationStateProps> = ({ status }) => {
  const config = useConfig(status)
  return (
    <Text
      style={{
        ...textStyles.XSMedium,
        paddingHorizontal: 8,
        paddingVertical: 4,
        backgroundColor: config.backgroundColor,
        color: config.contentColor,
        textAlign: "center",
        borderRadius: 10,
      }}
    >
      {config.label}
    </Text>
  )
}

export default ApplicationState
