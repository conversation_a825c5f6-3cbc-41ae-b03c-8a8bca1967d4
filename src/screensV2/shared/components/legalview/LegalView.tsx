import React, { useCallback } from "react"
import { StyleSheet, View, Text, FlatList, Image, Linking } from "react-native"
import { useTranslation } from "react-i18next"
import { CollapseWithHeaderView } from "src/componentsv2/CollapseWithHeaderView"
import { CustomPressable, BaseBottomSheet } from "src/componentsv2"
import { textStyles, viewStyles } from "src/config/styles"
import { Divider } from "react-native-paper"
import Colors from "src/config/colors"
import {
  useLegalRequirement,
  DisplayLegalRequirement,
} from "./useLegalRequirement"
import { RequirementType } from "src/api"
import LegalRequirementState from "./LegalRequirementState"
import icBookOpenCheck from "assets/imagesV2/ic_book_open_check.png"
import icFile from "assets/imagesV2/ic_file.png"
import { t } from "i18next"
import { BSCSCAN_URL } from "src/config/env"
import DocumentsBottomSheet from "./DocumentsBottomSheet"

interface LegalViewProps {
  metadataId: number
  tokenMintEventTxHash: string
}

interface LegalRequirementItemViewProps {
  legalRequirement: DisplayLegalRequirement
  openDocuments: (requirementType: RequirementType) => void
}

const LegalRequirementItemView: React.FC<LegalRequirementItemViewProps> = ({
  legalRequirement,
  openDocuments,
}) => {
  const { groupTypeTitle, requirementTypeName, requirementType, fileUrl } =
    legalRequirement

  const isCompleted = fileUrl !== ""

  return (
    <View style={[styles.row, styles.marginBottom8]}>
      <Text style={styles.label}>{groupTypeTitle}</Text>
      <Text style={styles.label}>{requirementTypeName}</Text>
      <View style={styles.legalRequirementState}>
        <LegalRequirementState isCompleted={isCompleted} />
      </View>
      {isCompleted ? (
        <CustomPressable
          style={styles.fileButton}
          onPress={() => openDocuments(requirementType)}
        >
          <Image source={icFile} style={viewStyles.size12Icon} />
          <Text style={styles.files}>{t("File(s)")}</Text>
        </CustomPressable>
      ) : (
        <Text style={styles.notUploaded}>{t("Not uploaded")}</Text>
      )}
    </View>
  )
}

const LegalTableHeader: React.FC = () => {
  const { t } = useTranslation()

  return (
    <View style={styles.rowItem}>
      <Text style={styles.headerLabel}>{t("Stage")}</Text>
      <Text style={styles.headerLabel}>{t("Documents")}</Text>
      <Text style={[styles.headerLabel, { textAlign: "center" }]}>
        {t("Status")}
      </Text>
      <Text style={[styles.headerLabel, { textAlign: "right" }]}>
        {t("Attachment")}
      </Text>
    </View>
  )
}

const LegalView: React.FC<LegalViewProps> = ({
  metadataId,
  tokenMintEventTxHash,
}) => {
  const { t } = useTranslation()
  const {
    showingDocumentFiles,
    showingIssuerName,
    selectLegalRequirement,
    isOpenBottomSheet,
    setIsOpenBottomSheet,
    legalRequirements,
  } = useLegalRequirement(metadataId, tokenMintEventTxHash)

  const handleOnClose = () => {
    setIsOpenBottomSheet(false)
  }

  const handleOpenDocuments = (requirementType: RequirementType) => {
    if (requirementType === RequirementType.MORTGAGE_STATUS) {
      const url = `${BSCSCAN_URL}/tx/${tokenMintEventTxHash}`
      Linking.openURL(url)
    } else {
      selectLegalRequirement(requirementType)
    }
  }

  const renderItem = useCallback(
    (item: DisplayLegalRequirement, index: number) => (
      <LegalRequirementItemView
        legalRequirement={item}
        key={index}
        openDocuments={handleOpenDocuments}
      />
    ),
    []
  )

  return (
    <>
      <Divider style={styles.divider} />
      <View style={styles.container}>
        <CollapseWithHeaderView
          title={t("Legal")}
          headerIconUri={icBookOpenCheck}
          emptyTitle={t("No legal requirement")}
        >
          <View style={styles.content}>
            <FlatList
              ListHeaderComponent={<LegalTableHeader />}
              style={styles.list}
              scrollEnabled={false}
              data={legalRequirements}
              renderItem={({ item, index }) => renderItem(item, index)}
              keyExtractor={(_, index) => index.toString()}
              numColumns={1}
            />
          </View>
        </CollapseWithHeaderView>
      </View>
      <BaseBottomSheet
        title={showingIssuerName}
        visible={isOpenBottomSheet}
        onClose={handleOnClose}
      >
        <DocumentsBottomSheet documentFiles={showingDocumentFiles} />
      </BaseBottomSheet>
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingVertical: 16,
  },
  list: {
    width: "100%",
  },
  divider: {
    backgroundColor: Colors.Neutral950,
    height: 1,
  },
  headerLabel: {
    flex: 1,
    ...textStyles.MRegular,
    color: Colors.Neutral500,
  },
  marginBottom8: {
    marginBottom: 8,
  },
  legalRequirementState: {
    flex: 1,
    alignItems: "center",
  },
  content: {
    marginTop: 16,
    width: "100%",
  },
  rowItem: {
    flexDirection: "row",
    width: "100%",
    alignItems: "center",
    marginBottom: 16,
  },
  label: {
    ...textStyles.MRegular,
    flex: 1,
    color: Colors.PalleteWhite,
  },
  value: {
    ...textStyles.MRegular,
    color: Colors.PalleteWhite,
    marginStart: 4,
  },
  detailItem: {
    flexDirection: "row",
  },
  labelItem: {
    ...textStyles.MRegular,
    color: Colors.Neutral300,
    flex: 1,
  },
  pressable: {
    flexDirection: "row",
  },
  link: {
    ...textStyles.MRegular,
    color: Colors.Secondary300,
    marginHorizontal: 4,
  },
  flex1: {
    flex: 1,
  },
  valueItem: {
    ...textStyles.MRegular,
    color: Colors.PalleteWhite,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  open: {
    ...viewStyles.size14Icon,
    marginStart: 4,
  },
  files: {
    ...textStyles.MRegular,
    color: Colors.PalleteWhite,
    marginStart: 4,
  },
  notUploaded: {
    ...textStyles.MRegular,
    flex: 1,
    textAlign: "right",
    color: Colors.Neutral500,
  },
  fileButton: {
    flex: 1,
    alignItems: "center",
    justifyContent: "flex-end",
    flexDirection: "row",
    height: "100%",
  },
})

export default LegalView
