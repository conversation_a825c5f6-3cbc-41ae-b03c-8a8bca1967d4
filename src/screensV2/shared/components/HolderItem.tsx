import React from "react"
import { StyleSheet, View, Text } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import BoxInfoView from "./BoxInfoView"
import { AddressView } from "src/componentsv2"
import { Divider } from "react-native-paper"

interface HolderItemProps {
  fromAddress: string
  amount: string
  value: string
  currency: string
}

const HolderItem: React.FC<HolderItemProps> = ({
  fromAddress,
  amount,
  value,
  currency,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <Text style={styles.label}>{t("From")}</Text>
        <AddressView
          address={fromAddress}
          copy={true}
          style={{ marginStart: 4 }}
        />
      </View>
      <View style={[styles.row, { marginTop: 6, gap: 6 }]}>
        <BoxInfoView>
          <Text style={styles.label}>{t("Amount")}</Text>
          <Text style={styles.value}>{amount} NFT</Text>
        </BoxInfoView>
        <BoxInfoView>
          <Text style={styles.label}>{t("Value")}</Text>
          <Text style={styles.value}>
            {value} {currency}
          </Text>
        </BoxInfoView>
      </View>
      <Divider
        style={{ marginTop: 12, backgroundColor: Colors.Neutral950, height: 1 }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 12,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    ...textStyles.SMedium,
    color: Colors.Neutral300,
  },
  value: {
    ...textStyles.SMedium,
    color: Colors.white,
    marginStart: 4,
  },
})

export default HolderItem
