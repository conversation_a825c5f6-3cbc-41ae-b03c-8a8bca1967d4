import React from "react"
import { View, Text, StyleSheet } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { formatChainlinkTimestamp } from "src/utils/timeExt"
interface ChainlinkPriceFeedPopupProps {
  isVisible: boolean
  onClose: () => void
  chainlinkTimestamp: string
}

const ChainlinkPriceFeedPopup: React.FC<ChainlinkPriceFeedPopupProps> = ({
  isVisible,
  onClose,
  chainlinkTimestamp,
}) => {
  const { t } = useTranslation()

  if (!isVisible) return null

  const formattedTimestamp = formatChainlinkTimestamp(chainlinkTimestamp)

  return (
    <View style={styles.container}>
      <View style={styles.arrow} />

      <View style={styles.contentContainer}>
        <Text style={styles.title}>
          {t("Price based on Chainlink feed at:")}
        </Text>
        <Text style={styles.timestamp}>{formattedTimestamp}</Text>
        <Text style={styles.gotIt} onPress={onClose}>
          {t("Got it")}
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    overflow: "visible",
    position: "absolute",
    marginTop: 6,
    top: "100%",
    zIndex: 1000,
  },
  arrow: {
    position: "absolute",
    top: -6,
    left: 16,
    width: 0,
    height: 0,
    backgroundColor: "transparent",
    borderStyle: "solid",
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderBottomWidth: 6,
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
    borderBottomColor: Colors.Neutral800,
  },
  contentContainer: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: Colors.Neutral800,
  },
  title: {
    ...textStyles.SMedium,
    color: Colors.Neutral300,
  },
  timestamp: {
    ...textStyles.SBold,
    marginTop: 2,
    marginBottom: 8,
  },
  gotIt: {
    ...textStyles.SSemiBold,
    color: Colors.Secondary300,
  },
})

export default ChainlinkPriceFeedPopup
