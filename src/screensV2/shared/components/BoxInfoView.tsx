import React from "react"
import { StyleProp, StyleSheet, View, ViewStyle } from "react-native"

import Colors from "src/config/colors"

interface BoxInfoViewProps {
  children: React.ReactNode
  style?: StyleProp<ViewStyle>
}

const BoxInfoView: React.FC<BoxInfoViewProps> = ({ children, style }) => {
  return <View style={[styles.infoBox, style]}>{children}</View>
}

const styles = StyleSheet.create({
  infoBox: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.Neutral950,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 6,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
})

export default BoxInfoView
