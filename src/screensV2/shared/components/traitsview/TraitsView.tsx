import React, { useCallback, useState } from "react"
import { Image, StyleSheet, Text, View, FlatList } from "react-native"
import { EstateTokenAttribute } from "src/api"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { CollapseWithHeaderView, CustomPressable } from "src/componentsv2"
import { Divider } from "react-native-paper"

import icTag from "assets/imagesV2/ic_tag.png"
import { useTraitConfig } from "./config"

const MAX_ITEMS = 6

interface EstateTokenTraitViewProps {
  estateTokenAttribute: EstateTokenAttribute
}

const EstateTokenAttributeView: React.FC<EstateTokenTraitViewProps> = ({
  estateTokenAttribute,
}) => {
  const trait = useTraitConfig(estateTokenAttribute.trait_type)

  return (
    <View style={styles.traitItem}>
      <View style={styles.itemIcon}>
        <Image source={trait.icon} style={viewStyles.size18Icon} />
      </View>
      <View style={styles.itemContent}>
        <Text style={styles.traitType}>{trait.title}</Text>
        <Text numberOfLines={2} style={styles.traitValue}>
          {estateTokenAttribute.value}
        </Text>
      </View>
    </View>
  )
}

interface TraitsViewProps {
  estateTokenTraits: EstateTokenAttribute[]
}

const TraitsView: React.FC<TraitsViewProps> = ({ estateTokenTraits }) => {
  const { t } = useTranslation()
  const [isExpanded, setIsExpanded] = useState(false)

  const renderItem = useCallback(
    (item: EstateTokenAttribute, index: number) => (
      <EstateTokenAttributeView estateTokenAttribute={item} key={index} />
    ),
    []
  )

  const visibleTraits = isExpanded
    ? estateTokenTraits
    : estateTokenTraits.slice(0, MAX_ITEMS)

  const hasMoreItems = estateTokenTraits.length > MAX_ITEMS

  return (
    <>
      <Divider style={styles.divider} />
      <View style={styles.container}>
        <CollapseWithHeaderView
          title={t("Traits")}
          showEmpty={estateTokenTraits.length === 0}
          headerIconUri={icTag}
          emptyTitle={t("No traits")}
        >
          <FlatList
            style={styles.list}
            scrollEnabled={false}
            data={visibleTraits}
            renderItem={({ item, index }) => renderItem(item, index)}
            keyExtractor={(_, index) => index.toString()}
            numColumns={1}
            contentContainerStyle={styles.traitsContainer}
          />
          {hasMoreItems && (
            <CustomPressable
              style={styles.expandButton}
              onPress={() => setIsExpanded(!isExpanded)}
            >
              <Text style={styles.buttonText}>
                {isExpanded
                  ? t("Show less")
                  : `${t("Show all traits", { traitsSize: estateTokenTraits.length })}`}
              </Text>
            </CustomPressable>
          )}
        </CollapseWithHeaderView>
      </View>
    </>
  )
}

const styles = StyleSheet.create({
  divider: {
    backgroundColor: Colors.Neutral950,
    height: 1,
  },
  container: {
    width: "100%",
    marginVertical: 16,
  },
  traitsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    padding: 12,
  },
  list: {
    marginTop: 16,
    width: "100%",
  },
  itemIcon: {
    padding: 12,
    backgroundColor: Colors.PalleteBlack,
    borderRadius: 2,
  },
  traitItem: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.Neutral950,
    borderRadius: 4,
    padding: 6,
    marginBottom: 8,
  },
  itemContent: {
    marginStart: 6,
    flex: 1,
  },
  traitType: {
    ...textStyles.SMedium,
    color: Colors.PalleteWhite,
  },
  traitValue: {
    ...textStyles.bodyM,
    marginTop: 4,
    alignContent: "center",
    flexWrap: "wrap",
    color: Colors.Neutral500,
  },
  expandButton: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingVertical: 10,
    borderRadius: 4,
    marginTop: 8,
  },
  buttonText: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
})

export { TraitsView }
