import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { AreaUnitView, CardView } from "src/componentsv2"
import fileBoxIcon from "assets/imagesV2/ic_file_box.png"
import mapPinIcon from "assets/imagesV2/ic_map_pin.png"
import scalingIcon from "assets/imagesV2/ic_scaling.png"
import InfoItem from "./InfoItem"
import { EstateTokenAreaUnit } from "src/api/types/estate"
import { useTranslation } from "react-i18next"
import { calculateGridItemDimensions } from "src/utils/layout"
import { CustomPressable } from "src/componentsv2/CustomPressable"
import { formatCurrency } from "src/utils/format"

interface BaseGridViewItemProps {
  name: string
  imageUrl: string
  zone: string
  area: {
    area: number
    unit: EstateTokenAreaUnit
  }
  totalSupply: string
  buttonView?: React.ReactNode
  onPress?: () => void
}

const { itemWidth } = calculateGridItemDimensions({
  numColumns: 2,
})

const BaseGridViewItem: React.FC<BaseGridViewItemProps> = ({
  name,
  imageUrl,
  zone,
  area,
  totalSupply,
  buttonView,
  onPress,
}) => {
  const { t } = useTranslation()

  return (
    <CardView style={styles.card}>
      <CustomPressable onPress={onPress}>
        <View style={styles.container}>
          <Image source={{ uri: imageUrl }} style={styles.image} />

          <View style={styles.contentContainer}>
            <Text style={styles.title} numberOfLines={2}>
              {name}
            </Text>

            <View style={styles.infoRow}>
              <InfoItem
                icon={fileBoxIcon}
                iconStyle={styles.infoItemIcon}
                text={`${formatCurrency(totalSupply)} ${t("NFT")}`}
              />
              <Text style={styles.dot}>•</Text>
              <InfoItem
                icon={mapPinIcon}
                iconStyle={styles.infoItemIcon}
                text={zone}
              />
              <Text style={styles.dot}>•</Text>
              <View style={styles.areaContainer}>
                <Image
                  source={scalingIcon}
                  style={[viewStyles.size8Icon, styles.infoItemIcon]}
                />
                {area && area.unit && (
                  <AreaUnitView area={area.area} areaUnit={area.unit} />
                )}
              </View>
            </View>
          </View>
        </View>

        {buttonView}
      </CustomPressable>
    </CardView>
  )
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    flex: 1,
    width: itemWidth,
  },
  container: {
    width: "100%",
    flex: 1,
    padding: 6,
  },
  image: {
    borderRadius: 6,
    height: 100,
    resizeMode: "cover",
    marginBottom: 6,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    ...textStyles.SMedium,
    color: Colors.white,
    marginBottom: 6,
    minHeight: 24,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoItemIcon: {
    tintColor: Colors.PalleteWhite,
  },
  dot: {
    color: Colors.white,
    marginHorizontal: 3,
  },
  areaContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
})

export default BaseGridViewItem
