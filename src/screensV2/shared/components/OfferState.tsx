import React from "react"
import { StyleSheet, Text, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"

interface OfferStateConfig {
  color: string
  label: string
  textColor: string
}

interface OfferStateProps {
  style?: ViewStyle
  state: string
}

const getOfferStateConfig = (
  state: string,
  t: (key: string) => string
): OfferStateConfig => {
  const configs: Record<string, OfferStateConfig> = {
    CANCELLED: {
      color: Colors.Danger900,
      label: t("Cancelled"),
      textColor: Colors.Danger500,
    },
    SOLD: {
      color: Colors.Danger900,
      label: t("Sold"),
      textColor: Colors.Danger500,
    },
    SELLING: {
      color: Colors.Success900,
      label: t("Selling"),
      textColor: Colors.Success500,
    },
  }

  return (
    configs[state] || {
      color: Colors.Danger900,
      label: t("Unknown status"),
      textColor: Colors.Danger500,
    }
  )
}

const OfferState: React.FC<OfferStateProps> = ({ state, style }) => {
  const { t } = useTranslation()
  const config = getOfferStateConfig(state, t)

  return (
    <Text
      style={[
        styles.status,
        { backgroundColor: config.color, color: config.textColor },
        style,
      ]}
    >
      {config.label}
    </Text>
  )
}

const styles = StyleSheet.create({
  status: {
    ...textStyles.XSMedium,
    alignSelf: "flex-start",
    borderRadius: 999,
    padding: 4,
  },
})
export default OfferState
