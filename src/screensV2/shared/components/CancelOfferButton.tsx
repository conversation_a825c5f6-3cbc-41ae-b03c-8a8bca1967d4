import React, { useState } from "react"
import { Alert, StyleSheet } from "react-native"
import { SimpleLoadingView } from "src/componentsv2"
import { PrimaryButton } from "src/componentsv2/Button"
import { MarketplaceOffer } from "src/api"
import { useTranslation } from "react-i18next"
import { CONTRACT_ADDRESS_ESTATE_MARKETPLACE } from "src/config/env"
import { useEthersProvider } from "hooks"
import { useAccount, useWriteContract } from "wagmi"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import Logger from "src/utils/logger"
import { estateMarketplaceAbi } from "src/api/contracts"

interface CancelOfferButtonProps {
  offer: Omit<MarketplaceOffer, "seller">
  onRefresh?: () => void
  buttonWidth?: number
  buttonHeight?: number
  borderRadius?: number
}

const logger = new Logger({ tag: "CancelOfferButton" })

export const CancelOfferButton: React.FC<CancelOfferButtonProps> = ({
  offer,
  onRefresh,
  buttonWidth,
  buttonHeight,
  borderRadius,
}) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [canCancelLoading, setCanCancelLoading] = useState<boolean>(true)

  const ethersProvider = useEthersProvider()
  const { address } = useAccount()
  const { writeContractAsync } = useWriteContract()

  const openCancelOfferAlert = () =>
    Alert.alert(
      t("Cancel Offer"),
      t(
        "Are you sure you want to cancel this offer? This action cannot be undone."
      ),
      [
        {
          text: t("No"),
          onPress: () => {},
          style: "cancel",
        },
        { text: t("Yes"), onPress: handleCancelOffer },
      ],
      { cancelable: false }
    )

  const handleCancelOffer = async () => {
    if (isLoading || !ethersProvider) return
    setIsLoading(true)
    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_MARKETPLACE,
        abi: estateMarketplaceAbi,
        functionName: "cancel",
        args: [offer.id],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status !== 1) {
        throw new Error(t("Fail to cancel offer"))
      } else {
        showSuccessWhenCallContract(
          t("Offer has been cancelled") +
            ". " +
            t("Data will be updated in few seconds")
        )
        onRefresh?.()
      }
    } catch (error) {
      showError(t("Fail to cancel offer"))
      logger.error("Failed to cancel offer", error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!address) {
    return null
  }

  return (
    <>
      <PrimaryButton
        title={t("Cancel")}
        onPress={openCancelOfferAlert}
        width={buttonWidth}
        height={buttonHeight}
        borderRadius={borderRadius}
        color={Colors.Danger500}
        contentColor={Colors.PalleteWhite}
        textStyle={styles.buttonText}
        enabled={Boolean(address)}
      />
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

const styles = StyleSheet.create({
  buttonText: {
    ...textStyles.SMedium,
    textAlign: "center",
  },
})
