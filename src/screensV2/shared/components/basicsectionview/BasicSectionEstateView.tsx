import React from "react"
import { View, Text, Image, StyleSheet, Pressable } from "react-native"
import SlideImageView from "../SlideImageView"
import { EstateTokenArea, LocaleDetail } from "src/api/types/estate"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { shortenAddress } from "src/utils/stringExt"
import InfoItem from "../InfoItem"
import clockIcon from "assets/imagesV2/ic_clock.png"
import mapPinIcon from "assets/imagesV2/ic_map_pin.png"
import fileBoxIcon from "assets/imagesV2/ic_file_box.png"
import scalingIcon from "assets/imagesV2/ic_scaling.png"
import { AreaUnitView } from "src/componentsv2/AreaUnitView"
import { getElapsedTime } from "src/utils/timeExt"
import { getFullAddress } from "src/api/types/estate"
import { useNavigateProfile } from "../../hooks/useNavigateProfile"
import { formatCurrency } from "src/utils/format"

interface BasicSectionEstateViewProps {
  images: string[]
  name: string
  sellerAvatar: string
  sellerAddress: string
  createdAt: number
  address: string
  totalSupply: string
  area: EstateTokenArea
  locale_detail: LocaleDetail
}

const BasicSectionEstateView: React.FC<BasicSectionEstateViewProps> = ({
  images,
  name,
  sellerAvatar,
  sellerAddress,
  createdAt,
  address,
  totalSupply = "0",
  area,
  locale_detail,
}) => {
  const { t } = useTranslation()
  const sellerAddressShortened = shortenAddress(sellerAddress)
  const onNavigateToProfile = useNavigateProfile()

  const timestamp = new Date(createdAt).getTime()
  const elapsedTime = getElapsedTime(timestamp, t)
  const createdAtFormatted = t("Posted on") + " " + elapsedTime

  const fullAddress = getFullAddress(address, locale_detail)

  const formattedTotalSupply = `${t("Total Supply")}: ${formatCurrency(totalSupply)} ${t("NFTs")}`

  return (
    <View style={styles.container}>
      <SlideImageView images={images} />
      <Text style={styles.name} numberOfLines={2}>
        {name}
      </Text>
      <Pressable
        style={styles.creatorContainer}
        onPress={() => {
          onNavigateToProfile(sellerAddress)
        }}
      >
        <Text style={styles.byText}>{t("By")}</Text>
        {sellerAvatar && (
          <Image
            source={{
              uri: sellerAvatar,
            }}
            style={styles.avatar}
          />
        )}
        <Text style={styles.creatorName}>{sellerAddressShortened}</Text>
      </Pressable>
      <InfoItem
        style={styles.infoRow}
        icon={clockIcon}
        text={createdAtFormatted}
        iconStyle={styles.infoIcon}
        textStyle={styles.infoText}
      />
      <InfoItem
        style={[styles.infoRow, { flex: 1 }]}
        icon={mapPinIcon}
        text={fullAddress}
        iconStyle={styles.infoIcon}
        textStyle={[styles.infoText, { flex: 1 }]}
      />

      <InfoItem
        style={styles.infoRow}
        icon={fileBoxIcon}
        text={formattedTotalSupply}
        iconStyle={styles.infoIcon}
        textStyle={styles.infoText}
      />
      <View style={styles.areaContainer}>
        <InfoItem
          icon={scalingIcon}
          text={`${t("Acreage")}: `}
          iconStyle={styles.infoIcon}
          textStyle={styles.infoText}
        />
        <AreaUnitView
          area={area.area}
          areaUnit={area.unit}
          textStyle={styles.infoText}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 16,
  },
  name: {
    ...textStyles.size3XLMedium,
    color: Colors.PalleteWhite,
    marginBottom: 8,
    marginTop: 16,
  },
  creatorContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  byText: {
    ...textStyles.XLBold,
    color: Colors.PalleteWhite,
    marginRight: 6,
  },
  avatar: {
    ...viewStyles.size20Icon,
    resizeMode: "cover",
    marginRight: 6,
  },
  creatorName: {
    ...textStyles.XLBold,
    color: Colors.PalleteWhite,
  },
  infoRow: {
    marginBottom: 8,
  },
  infoIcon: {
    ...viewStyles.size16Icon,
    marginRight: 8,
    tintColor: Colors.Neutral300,
  },
  infoText: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  areaContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
})

export default BasicSectionEstateView
