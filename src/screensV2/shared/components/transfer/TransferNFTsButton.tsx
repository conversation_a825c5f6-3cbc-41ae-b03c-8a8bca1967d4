import { SecondaryButton } from "src/componentsv2/Button"
import React, { useState } from "react"
import { ViewStyle } from "react-native"
import { TransferNFTsModal } from "./TransferNFTsModal"
import { useAccount } from "wagmi"
import { useEstateTokenBalance } from "src/api/contracts"

interface TransferNFTsButtonProps {
  estateId: string
  style?: ViewStyle
}

export const TransferNFTsButton: React.FC<TransferNFTsButtonProps> = ({
  estateId,
  style,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const { address } = useAccount()

  const { value: nftBalance } = useEstateTokenBalance(
    address as `0x${string}`,
    estateId
  )

  const isBalancePositive = Number(nftBalance) > 0

  return (
    <>
      <SecondaryButton
        title="Transfer NFTs"
        onPress={() => setIsOpen(true)}
        enabled={isBalancePositive}
        style={style}
      />
      <TransferNFTsModal
        estateId={estateId}
        visible={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  )
}
