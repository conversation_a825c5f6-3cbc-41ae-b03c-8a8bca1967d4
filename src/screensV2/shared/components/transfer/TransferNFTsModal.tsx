import React from "react"
import { StyleSheet, Text, View, Image } from "react-native"
import { Controller } from "react-hook-form"
import { PrimaryButton } from "src/componentsv2/Button"
import { InputField, BaseModal, CustomPressable } from "src/componentsv2"
import icNetwork from "assets/imagesV2/ic_bsc_network.png"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTransfer } from "./hooks/useTransfer"

interface Props {
  estateId: string
  visible: boolean
  onClose: () => void
}

export const TransferNFTsModal: React.FC<Props> = ({
  estateId,
  visible,
  onClose,
}) => {
  const {
    form,
    isLoading,
    nftBalance,
    handleSubmit,
    handleSetMaxBalance,
    closeAndReset,
    chainName,
    t,
    estateDecimals,
  } = useTransfer({ estateId, onClose })
  return (
    <BaseModal
      visible={visible}
      onClose={closeAndReset}
      title={t("Transfer NFTs")}
      isShowCloseIcon={true}
    >
      <View style={styles.container}>
        <Text style={styles.label}>{t("Network")}</Text>
        <View style={styles.networkRow}>
          <Image source={icNetwork} style={viewStyles.size16Icon} />
          <Text style={styles.chainName}>{chainName}</Text>
        </View>
        <View style={styles.nftRow}>
          <Text style={styles.label}>{t("Enter NFT amount")}</Text>
          <Text
            style={styles.label}
          >{`${t("Available")}: ${nftBalance} ${t("NFTs")}`}</Text>
        </View>
        <Controller
          control={form.control}
          name="amount"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <View>
              <InputField
                value={value}
                onChangeText={onChange}
                type={"decimal"}
                decimalPlaces={estateDecimals}
                inputMode={"decimal"}
                placeholder="0.00"
                style={styles.input}
                error={error?.message}
              />
              <CustomPressable style={styles.max} onPress={handleSetMaxBalance}>
                <Text style={styles.maxText}>{t("Max")}</Text>
              </CustomPressable>
            </View>
          )}
        />
        <Controller
          control={form.control}
          name="toAddress"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <InputField
              label={t("Enter wallet destination")}
              value={value}
              onChangeText={onChange}
              style={styles.input}
              error={error?.message}
            />
          )}
        />
        <PrimaryButton
          title={isLoading ? t("Transferring...") : t("Transfer")}
          onPress={form.handleSubmit(handleSubmit)}
          borderRadius={8}
          height={38}
          textStyle={textStyles.LMedium}
          enabled={!isLoading && Number(nftBalance) > 0}
          style={styles.submitButton}
        />
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 16,
    width: "100%",
  },
  label: {
    ...textStyles.MMedium,
    marginBottom: 8,
    color: Colors.Neutral500,
  },
  input: {
    marginBottom: 16,
    width: "100%",
  },
  networkRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: Colors.Neutral900,
  },
  chainName: {
    ...textStyles.LMedium,
    color: Colors.PalleteWhite,
    marginStart: 6,
  },
  maxButton: {
    paddingHorizontal: 12,
  },
  submitButton: {
    marginTop: 8,
  },
  nftRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 16,
    marginBottom: 8,
  },
  max: {
    position: "absolute",
    right: 8,
    transform: [{ translateY: 6 }],
  },
  maxText: {
    ...textStyles.MMedium,
    paddingVertical: 5,
    paddingHorizontal: 8,
    borderRadius: 999,
    borderWidth: 1,
    color: Colors.Secondary500,
    borderColor: Colors.Secondary500,
  },
})
