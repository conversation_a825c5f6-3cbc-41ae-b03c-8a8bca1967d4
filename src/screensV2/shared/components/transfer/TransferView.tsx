import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { useAccount } from "wagmi"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useEstateTokenBalance } from "src/api/contracts"
import { Estate } from "src/api"
import { CreateOfferButton } from "./CreateOfferButton"
import { TransferNFTsButton } from "./TransferNFTsButton"
import { useCurrencies } from "../../hooks/useCurrencies"
import { formatCurrency } from "src/utils"

import { useTranslation } from "react-i18next"
import icBox from "assets/imagesV2/ic_box.png"
import { formatNumericByDecimalsToNumber } from "src/utils/numberExt"

interface TransferViewProps {
  estate: Estate
}

const TransferView: React.FC<TransferViewProps> = ({ estate }) => {
  const { t } = useTranslation()
  const { address } = useAccount()
  const {
    tokenizationRequest: { currency, unitPrice, decimals },
  } = estate

  const { tokenSymbol } = useCurrencies(currency)

  const { value: nftBalance } = useEstateTokenBalance(
    address as `0x${string}`,
    estate.id
  )

  const unitPriceParsed = formatNumericByDecimalsToNumber(unitPrice, decimals)

  return (
    <View style={styles.container}>
      <Text style={{ color: Colors.white }}>{t("My NFTs")}</Text>
      <View style={styles.nftRow}>
        <Image source={icBox} style={viewStyles.size24Icon} />
        <Text style={styles.nft}>{`${formatCurrency(nftBalance)} NFT`}</Text>
      </View>
      <Text
        style={styles.unitPrice}
      >{`~${formatCurrency(unitPriceParsed * Number(nftBalance))} ${tokenSymbol}`}</Text>
      <View style={styles.buttonRow}>
        <TransferNFTsButton
          estateId={estate.id}
          style={{ flex: 1, marginEnd: 6 }}
        />
        <CreateOfferButton
          estate={estate}
          style={{ flex: 1, marginStart: 6 }}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    backgroundColor: Colors.Neutral950,
    marginVertical: 16,
  },
  myNft: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  nft: {
    ...textStyles.size3XLMedium,
    color: Colors.PalleteWhite,
    marginStart: 4,
  },
  nftRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 6,
    marginTop: 16,
  },
  content: {
    marginTop: 16,
  },
  unitPrice: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  buttonRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 16,
  },
})

export default TransferView
