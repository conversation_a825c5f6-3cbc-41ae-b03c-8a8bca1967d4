import { Distribution } from "src/api"
import { formatEther } from "viem"

export const useInvestmentDistribution = (distribution: Distribution) => {
  const now = new Date().getTime() / 1000
  const totalAmount = parseFloat(formatEther(BigInt(distribution.totalAmount)))
  const withdrawnAmount = parseFloat(
    formatEther(BigInt(distribution.withdrawnAmount))
  )
  const currentUnlocked =
    now >= distribution.vestingEndsAtInSeconds
      ? totalAmount
      : (totalAmount * (now - distribution.distributeAt)) /
        (distribution.vestingEndsAtInSeconds - distribution.distributeAt)
  const withdrawableAmount = currentUnlocked - withdrawnAmount

  return {
    totalAmount,
    currentUnlocked,
    withdrawnAmount,
    withdrawableAmount,
  }
}
