import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { Distribution } from "src/api"
import iconBrik from "assets/imagesV2/ic_brik.png"
import { useInvestmentDistribution } from "./useInvestmentDistribution"
interface InvestmentDistributionViewProps {
  distribution: Distribution
  index: number
}

const InvestmentDistributionView: React.FC<InvestmentDistributionViewProps> = ({
  distribution,
  index,
}) => {
  const { t } = useTranslation()
  const { totalAmount, currentUnlocked, withdrawnAmount, withdrawableAmount } =
    useInvestmentDistribution(distribution)
  return (
    <View
      style={[
        styles.cardMobile,
        index === 0 && { borderTopWidth: 0, paddingTop: 0 },
      ]}
    >
      <View style={styles.headerContainer}>
        <View style={styles.idGroup}>
          <Text style={[styles.idLabel, textStyles.SMedium]}>{t("ID")}</Text>
          <Text style={[styles.idValue, textStyles.SMedium]}>
            {String(distribution.distributionId).padStart(3, "0")}
          </Text>
        </View>
        <View style={styles.vestingGroup}>
          <View style={styles.vestingIconText}>
            <Image source={iconBrik} style={viewStyles.size10Icon} />
            <Text style={[styles.vestingLabel, textStyles.SMedium]}>
              {t("Vesting Until")}
            </Text>
          </View>
          <Text style={[styles.vestingDate, textStyles.SMedium]}>
            {new Date(
              distribution.vestingEndsAtInSeconds * 1000
            ).toLocaleDateString()}
          </Text>
        </View>
      </View>

      <View style={styles.detailsContainer}>
        <View style={styles.detailRow}>
          <View style={styles.detailCard}>
            <Text style={[styles.detailLabel, textStyles.SMedium]}>
              {t("Allocated")}
            </Text>
            <View style={styles.amountGroup}>
              <Text style={[styles.amountValue, textStyles.SMedium]}>
                {totalAmount.toFixed(2)}
              </Text>
              <Image source={iconBrik} style={styles.coinIcon} />
            </View>
          </View>

          <View style={styles.detailCard}>
            <Text style={[styles.detailLabel, textStyles.SMedium]}>
              {t("Unlocked")}
            </Text>
            <View style={styles.amountGroup}>
              <Text style={[styles.amountValue, textStyles.SMedium]}>
                {currentUnlocked.toFixed(2)}
              </Text>
              <Image source={iconBrik} style={styles.coinIcon} />
            </View>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.detailCard}>
            <Text style={[styles.detailLabel, textStyles.SMedium]}>
              {t("Withdrawn")}
            </Text>
            <View style={styles.amountGroup}>
              <Text style={[styles.amountValue, textStyles.SMedium]}>
                {withdrawnAmount.toFixed(2)}
              </Text>
              <Image source={iconBrik} style={styles.coinIcon} />
            </View>
          </View>

          <View style={styles.detailCard}>
            <Text style={[styles.detailLabel, textStyles.SMedium]}>
              {t("Withdrawable")}
            </Text>
            <View style={styles.amountGroup}>
              <Text style={[styles.amountValue, textStyles.SMedium]}>
                {withdrawableAmount.toFixed(2)}
              </Text>
              <Image source={iconBrik} style={styles.coinIcon} />
            </View>
          </View>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  cardMobile: {
    flexDirection: "column",
    alignItems: "flex-start",
    paddingVertical: 12,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.Neutral900,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  idGroup: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  idLabel: {
    color: Colors.Neutral500,
  },
  idValue: {
    color: Colors.PalleteWhite,
  },
  vestingGroup: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  vestingIconText: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
  },
  vestingLabel: {
    color: Colors.Neutral500,
  },
  vestingDate: {
    color: Colors.PalleteWhite,
  },
  detailsContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
    gap: 6,
    width: "100%",
  },
  detailRow: {
    flexDirection: "row",
    gap: 6,
    width: "100%",
    alignItems: "stretch", // Đảm bảo các card cao bằng nhau
  },
  detailCard: {
    flex: 1,
    flexBasis: 0, // Đảm bảo chia đều chiều rộng
    minWidth: 0, // Cho phép shrink khi cần thiết
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 8,
    gap: 4,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    borderRadius: 4,
  },
  detailLabel: {
    color: Colors.Neutral500,
  },
  amountGroup: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 2,
  },
  amountValue: {
    color: Colors.PalleteWhite,
    textAlign: "center",
  },
  coinIcon: {
    width: 10,
    height: 10,
    borderRadius: 999, // Makes it a circle
  },
})

export default InvestmentDistributionView
