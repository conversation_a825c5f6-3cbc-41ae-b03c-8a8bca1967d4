import { ImageSourcePropType, ViewStyle } from "react-native"
import {
  Investment,
  InvestmentRound,
  InvestmentRoundState,
} from "src/api/types/oracle"
import { TFunction } from "i18next"
import Colors from "src/config/colors"
import icCircleCheck from "assets/imagesV2/ic_circle_check.png"
import icLock from "assets/imagesV2/ic_lock.png"
import { formatPercentage } from "src/utils"
import { PERCENTAGE_PRECISION_MULTIPLIER } from "../../consts"

interface InvestmentRoundStateConfig {
  icon?: ImageSourcePropType
  backgroundColor: string
  roundNameColor: string
  roundStateColor: string
  roundStateText: string
  iconBackground?: ViewStyle
  containerStyle?: ViewStyle
}

export const useInvestmentRoundItem = (
  investmentRound: InvestmentRound,
  roundNameMap: Record<string, string>,
  t: TFunction
) => {
  const getRoundStateConfig = (state: string): InvestmentRoundStateConfig => {
    const roundStateMap: Record<string, InvestmentRoundStateConfig> = {
      FINISHED: {
        icon: icCircleCheck,
        backgroundColor: Colors.PalleteBlack,
        roundNameColor: Colors.Neutral500,
        roundStateColor: Colors.Neutral600,
        containerStyle: {
          borderWidth: 1,
          borderColor: Colors.Neutral900,
          backgroundColor: Colors.PalleteBlack,
        },
        iconBackground: {
          backgroundColor: Colors.Neutral900,
          padding: 6,
          borderRadius: 999,
        },
        roundStateText: t("Done"),
      },
      PROGRESSING: {
        backgroundColor: Colors.PalleteBlack,
        roundNameColor: Colors.Neutral500,
        roundStateColor: Colors.Neutral600,
        containerStyle: {
          borderWidth: 1,
          borderColor: Colors.Neutral900,
          backgroundColor: Colors.PalleteBlack,
        },
        roundStateText: t("Live Now"),
      },
      PENDING: {
        icon: icLock,
        backgroundColor: Colors.Neutral900,
        roundNameColor: Colors.Neutral500,
        roundStateColor: Colors.Neutral600,
        iconBackground: {
          backgroundColor: Colors.Neutral950,
          padding: 6,
          borderRadius: 999,
        },
        roundStateText: t("Coming Soon"),
      },
    }
    return roundStateMap[state] || roundStateMap.PENDING
  }

  const stateConfig = getRoundStateConfig(investmentRound.state)
  const isLive = investmentRound.state === InvestmentRoundState.PROGRESSING
  const percent = useRaiseProgressPercent(investmentRound)

  return {
    stateConfig,
    isLive,
    percent,
    roundName: roundNameMap[investmentRound.round] || investmentRound.round,
  }
}

export const useRaiseProgressPercent = (
  investmentRound: InvestmentRound
): string => {
  const { tokenAllocation, investments = [] } = investmentRound
  const tokenAllocationBigInt = BigInt(tokenAllocation || "0")
  const totalAllocated = useTotalAllocated(investments)
  const percent =
    tokenAllocationBigInt > 0n
      ? formatPercentage(
          Number(
            (totalAllocated * PERCENTAGE_PRECISION_MULTIPLIER) /
              tokenAllocationBigInt
          ) / 10000.0
        )
      : "-"
  return percent
}

export const useTotalAllocated = (investments: Investment[]) => {
  const totalAllocated = (investments || []).reduce((total, investment) => {
    const filteredDistribution = (investment.distributions || []).filter(
      (distribution) =>
        !!distribution.distributionId && BigInt(distribution.totalAmount) > 0n
    )
    const investmentTotal = filteredDistribution.reduce((sum, distribution) => {
      return sum + BigInt(distribution.totalAmount)
    }, 0n)
    return total + investmentTotal
  }, 0n)
  return totalAllocated
}
