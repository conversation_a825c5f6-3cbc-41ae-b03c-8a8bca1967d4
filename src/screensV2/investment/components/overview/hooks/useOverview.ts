import { InvestmentRound } from "src/api/types/oracle"
import { useTranslation } from "react-i18next"
import { useInvestmentContext } from "src/screensV2/investment/context/InvestmentContext"
import { PROVIDER_CHAIN_ID } from "src/config/env"
import {
  useRaiseProgressPercent,
  useTotalAllocated,
} from "../../hooks/useInvestmentRoundItem"
import {
  PERCENTAGE_PRECISION_MULTIPLIER,
  TOTAL_TOKEN_SUPPLY,
} from "../../../consts"
import { formatPercentage } from "src/utils"

export const useOverview = () => {
  const { t } = useTranslation()
  const { selectedRound, roundNameMap, investmentRounds } =
    useInvestmentContext()
  const selectedRoundName = selectedRound && roundNameMap[selectedRound]
  const selectedInvestmentRound = investmentRounds.find(
    (round: InvestmentRound) => round.round === selectedRound
  )

  const chainNamesMap: Record<string, string> = {
    "56": t("BNB Chain"),
    "97": t("BNB Chain Testnet"),
  }

  const {
    investments = [],
    tokenAllocation = "0",
    state,
  } = selectedInvestmentRound || {}
  const tokenAllocationBigInt = BigInt(tokenAllocation)
  const totalAllocated = useTotalAllocated(investments)

  const chainName = chainNamesMap[PROVIDER_CHAIN_ID] || t("Unknown Chain")
  const percentageSupply = formatPercentage(
    Number(
      (tokenAllocationBigInt * PERCENTAGE_PRECISION_MULTIPLIER) /
        TOTAL_TOKEN_SUPPLY
    ) / 10000.0,
    4
  )

  const totalContributors = investments?.length || 0

  const raiseProgressPercent = selectedInvestmentRound
    ? useRaiseProgressPercent(selectedInvestmentRound)
    : "-"

  return {
    selectedRoundName,
    state,
    chainName,
    tokenAllocationBigInt,
    totalAllocated,
    percentageSupply,
    totalContributors,
    raiseProgressPercent,
    selectedInvestmentRound,
  }
}
