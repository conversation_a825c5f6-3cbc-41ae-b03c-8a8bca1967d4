import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import StatItemView from "../StatItemView"
import InvestorsView from "../InvestorsView"
import { Divider } from "react-native-paper"
import { formatCurrency, formatEtherWithPrecision } from "src/utils"
import icCirleCheck from "assets/imagesV2/ic_circle_check.png"
import { useOverview } from "./hooks/useOverview"
import { InvestmentRoundState } from "src/api"

const OverviewView: React.FC = () => {
  const { t } = useTranslation()
  const {
    selectedRoundName,
    state,
    chainName,
    tokenAllocationBigInt,
    totalAllocated,
    percentageSupply,
    totalContributors,
    raiseProgressPercent,
  } = useOverview()

  const isDone = state == InvestmentRoundState.FINISHED

  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Text style={textStyles.MSemiBold}>
          {t("Round Overview", { round: selectedRoundName })}
        </Text>
        <View
          style={[
            styles.badge,
            { backgroundColor: isDone ? Colors.Success500 : Colors.Success800 },
          ]}
        >
          {isDone ? (
            <>
              <Image source={icCirleCheck} style={viewStyles.size8Icon} />
              <Text style={styles.badgeText}>{t("Sale Completed")}</Text>
            </>
          ) : (
            <>
              <View style={styles.circle10}></View>
              <Text style={styles.badgeText}>{t("Live Now")}</Text>
            </>
          )}
        </View>
      </View>
      <View style={[styles.statsGrid, styles.marginBottom6]}>
        <StatItemView
          label={t("Network")}
          isShowNetworkIcon={true}
          value={chainName}
        />
        <StatItemView
          label={t("Tokens for Sale")}
          value={`${formatCurrency(formatEtherWithPrecision(tokenAllocationBigInt))} BRIK`}
          showBorder={false}
        />
      </View>
      <View style={styles.statsGrid}>
        <StatItemView
          label={t("Percentage Supply")}
          value={`${percentageSupply}%`}
        />
        <StatItemView
          label={t("Allocated")}
          value={`${formatCurrency(formatEtherWithPrecision(totalAllocated))} BRIK`}
        />
        <StatItemView
          label={t("Total Contributor")}
          value={totalContributors.toString()}
          showBorder={false}
        />
      </View>
      <View style={styles.rowSpaceBetween}>
        <Text style={styles.progressLabel}>{t("Raise Process")}</Text>
        <Text style={textStyles.XSBold}>{raiseProgressPercent}%</Text>
      </View>
      <View style={styles.progressBarBg}>
        <View
          style={[
            styles.progressBarFill,
            { width: `${Number(raiseProgressPercent)}%` },
          ]}
        />
      </View>
      <Divider style={styles.divider} />
      <InvestorsView />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    marginHorizontal: 16,
    padding: 8,
    backgroundColor: Colors.Neutral950,
    marginBottom: 16,
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  badge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.Success500,
    borderRadius: 999,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  badgeText: {
    ...textStyles.XSSemiBold,
    marginStart: 4,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 12,
    paddingVertical: 2,
  },
  networkRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  marginBottom6: {
    marginBottom: 6,
  },
  progressSection: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  progressLabel: {
    ...textStyles.XSMedium,
    color: Colors.Neutral500,
  },
  progressBarBg: {
    flex: 1,
    height: 10,
    borderRadius: 8,
    padding: 2,
    backgroundColor: Colors.PalleteBlack,
    overflow: "hidden",
  },
  progressBarFill: {
    height: "100%",
    backgroundColor: Colors.Success300,
    borderRadius: 8,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  vestingContainer: {
    marginTop: 12,
  },
  vestingTitle: {
    ...textStyles.MSemiBold,
    marginBottom: 12,
  },
  vestingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  vestingBox: {
    flex: 1,
    backgroundColor: Colors.Neutral900,
    borderRadius: 4,
    padding: 6,
    alignItems: "flex-start",
  },
  vestingLabel: {
    ...textStyles.XSMedium,
    color: Colors.Neutral400,
    marginBottom: 4,
  },
  divider: {
    height: 1,
    width: "100%",
    backgroundColor: Colors.Neutral900,
    marginVertical: 16,
  },
  marginHorizontal6: {
    marginHorizontal: 6,
  },
  circle10: {
    width: 10,
    height: 10,
    borderRadius: 999,
    backgroundColor: Colors.Success300,
  },
})

export default OverviewView
