import { useWriteContract } from "wagmi"
import { useEthersProvider } from "src/hooks/useEthersProvider"
import { useTranslation } from "react-i18next"
import { useState } from "react"
import { showSuccessWhenCallContract, showError } from "src/utils/toast"
import { driptributor<PERSON>bi } from "src/api/contracts/driptributor"
import { InvestmentRoundType } from "src/api"
import {
  CONTRACT_ADDRESS_BACKER_ROUND_DISTRIBUTOR,
  CONTRACT_ADDRESS_SEED_ROUND_DISTRIBUTOR,
  CONTRACT_ADDRESS_PRIVATE_SALE_1_DISTRIBUTOR,
  CONTRACT_ADDRESS_PRIVATE_SALE_2_DISTRIBUTOR,
  CONTRACT_ADDRESS_PUBLIC_SALE_AUCTION,
} from "src/config/env"

const useWithdrawAll = () => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const onWithdrawAll = async (ids: string[], round?: InvestmentRoundType) => {
    if (!round || round === InvestmentRoundType.BACKER) {
      return
    }
    const withdrawnIds = ids.map((id) => BigInt(id))
    if (!ethersProvider) {
      return
    }
    const contractAddress = getDistributorAddressByRound(round)
    if (!contractAddress) return

    try {
      setIsLoading(true)
      const txHash = await writeContractAsync({
        address: contractAddress,
        abi: driptributorAbi,
        functionName: "withdraw",
        args: [withdrawnIds],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Distribution withdraw success") +
            ". " +
            t("Data will be updated in few seconds")
        )
      } else {
        throw new Error(t("Distribution e" + receipt.transactionHash))
      }
    } catch (e: any) {
      showError(e.message)
    } finally {
      setIsLoading(false)
    }
  }

  return { onWithdrawAll, isLoading }
}

function getDistributorAddressByRound(
  round: InvestmentRoundType
): `0x${string}` | undefined {
  switch (round) {
    case InvestmentRoundType.BACKER:
      return CONTRACT_ADDRESS_BACKER_ROUND_DISTRIBUTOR
    case InvestmentRoundType.SEED:
      return CONTRACT_ADDRESS_SEED_ROUND_DISTRIBUTOR
    case InvestmentRoundType.PRIVATE_SALE_1:
      return CONTRACT_ADDRESS_PRIVATE_SALE_1_DISTRIBUTOR
    case InvestmentRoundType.PRIVATE_SALE_2:
      return CONTRACT_ADDRESS_PRIVATE_SALE_2_DISTRIBUTOR
    case InvestmentRoundType.PUBLIC_SALE:
      return CONTRACT_ADDRESS_PUBLIC_SALE_AUCTION
    default:
      return undefined
  }
}

export default useWithdrawAll
