import React from "react"
import { PrimaryButton } from "src/componentsv2/Button"
import { useTranslation } from "react-i18next"
import useWithdrawAll from "./useWithdraw"
import { useInvestmentContext } from "../../context/InvestmentContext"

interface WithdrawButtonProps {
  distributionIds: string[]
  enabled?: boolean
}
const WithdrawButton: React.FC<WithdrawButtonProps> = ({
  distributionIds,
  enabled = true,
}) => {
  const { t } = useTranslation()
  const { selectedRound } = useInvestmentContext()
  const { onWithdrawAll, isLoading } = useWithdrawAll()
  return (
    <PrimaryButton
      title={t("Withdraw All")}
      onPress={() => onWithdrawAll(distributionIds, selectedRound)}
      height={32}
      borderRadius={8}
      enabled={enabled}
      isLoading={isLoading}
    />
  )
}

export default WithdrawButton
