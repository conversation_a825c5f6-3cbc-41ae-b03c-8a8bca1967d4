import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import iconBrik from "assets/imagesV2/ic_brik.png"
import iconLockOpen from "assets/imagesV2/ic_lock_open.png"
import WithdrawButton from "../withdrawbutton"
import { useCurrentUnlockBalance } from "./useCurrentUnlockBalance"
import { Distribution } from "src/api/types/oracle"

interface CurrentUnlockBalanceProps {
  distributions: Distribution[]
}

const CurrentUnlockBalance: React.FC<CurrentUnlockBalanceProps> = ({
  distributions,
}) => {
  const { t } = useTranslation()
  const { currentUnlocked, distributionIds } = useCurrentUnlockBalance({
    distributions,
  })

  return (
    <View style={styles.currentBalanceContainer}>
      <View style={styles.currentBalanceContent}>
        <View style={styles.balanceIconAndText}>
          <View style={styles.brikIconWithLock}>
            <Image source={iconBrik} style={viewStyles.size32Icon} />
            <View style={styles.miniLockIconContainer}>
              <Image source={iconLockOpen} style={viewStyles.size8Icon} />
            </View>
          </View>
          <View style={styles.balanceTextContainer}>
            <Text style={[textStyles.SMedium, styles.currentBalanceLabel]}>
              {t("Current unlock balance")}
            </Text>
            <Text style={[textStyles.MSemiBold, styles.currentBalanceAmount]}>
              {currentUnlocked} BRIK
            </Text>
          </View>
        </View>
        <WithdrawButton
          enabled={currentUnlocked > 0}
          distributionIds={distributionIds}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  currentBalanceContainer: {
    backgroundColor: Colors.Neutral900,
    borderColor: Colors.Neutral900,
    borderWidth: 1,
    borderRadius: 6,
    padding: 12,
  },
  currentBalanceContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  balanceIconAndText: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  brikIconWithLock: {
    position: "relative",
  },
  miniLockIconContainer: {
    position: "absolute",
    right: -4,
    top: 0,
    backgroundColor: Colors.Neutral950,
    borderColor: Colors.Neutral900,
    borderWidth: 1,
    borderRadius: 999,
    width: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  balanceTextContainer: {
    gap: 2,
  },
  currentBalanceLabel: {
    color: Colors.Neutral500,
  },
  currentBalanceAmount: {
    color: Colors.PalleteWhite,
  },
})

export default CurrentUnlockBalance
