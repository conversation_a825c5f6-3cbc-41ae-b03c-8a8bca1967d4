import { useEffect, useRef, useState } from "react"
import { Distribution } from "src/api/types/oracle"
import { formatEther } from "viem"

interface UseCurrentUnlockBalanceProps {
  distributions: Distribution[]
}

export const useCurrentUnlockBalance = ({
  distributions,
}: UseCurrentUnlockBalanceProps) => {
  const [currentUnlocked, setCurrentUnlocked] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const totalWithdrawn = distributions.reduce((total, distribution) => {
    return total + parseFloat(formatEther(BigInt(distribution.withdrawnAmount)))
  }, 0)

  const calculateTotalUnlocked = () => {
    if (!distributions) return 0
    const currentTimeInSeconds = Math.floor(Date.now() / 1000)
    return distributions.reduce((total, distribution) => {
      const { distributeAt = 0, vestingEndsAtInSeconds = 0 } = distribution

      const totalAmount = parseFloat(
        formatEther(BigInt(distribution.totalAmount))
      )
      const currentDistributionUnlocked =
        (totalAmount * (currentTimeInSeconds - distributeAt)) /
        (vestingEndsAtInSeconds - distributeAt)
      return total + Math.min(currentDistributionUnlocked, totalAmount)
    }, 0)
  }

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      // Recalculate incrementPerSecond each time since current time changes
      const currentUnlocked = calculateTotalUnlocked()
      setCurrentUnlocked(currentUnlocked)
    }, 1000)

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }, [distributions])

  return {
    currentUnlocked: currentUnlocked - totalWithdrawn,
    distributionIds: distributions.map((d) => d.distributionId),
  }
}
