import React from "react"
import {
  View,
  Text,
  StyleSheet,
  ImageRequireSource,
  Image,
  ViewStyle,
} from "react-native"
import {
  formatCurrency,
  formatEtherWithPrecision,
  shortenAddress,
} from "src/utils"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { useInvestmentContext } from "src/screensV2/investment/context/InvestmentContext"
import { useTranslation } from "react-i18next"
import { Investment } from "src/api"
import { IconButton } from "src/componentsv2/Button"
import { showSuccess } from "utils/toast"
import * as Clipboard from "expo-clipboard"
import { useInvestor } from "./hooks/useInvestor"
import icCommonAvar from "assets/imagesV2/ic_common_avatar.png"
import icBrik from "assets/imagesV2/ic_brik.png"
import icCopy from "assets/imagesV2/ic_copy.png"

interface ItemViewProps {
  icon?: ImageRequireSource
  value: string
  style?: ViewStyle
  showBrikIcon?: boolean
  valueColor?: string
  handleCopy?: () => void
}

interface InvestorViewProps {
  investment: Investment
}

const ItemView: React.FC<ItemViewProps> = ({
  icon,
  value,
  style,
  showBrikIcon = true,
  handleCopy,
  valueColor = Colors.PalleteWhite,
}) => {
  return (
    <View style={[styles.holder, style]}>
      {icon && <Image source={icon} style={viewStyles.size10Icon} />}
      <Text style={[styles.value, { color: valueColor }]}>{value}</Text>
      {showBrikIcon && <Image source={icBrik} style={styles.brik} />}
      {handleCopy && (
        <IconButton
          style={styles.copy}
          onPress={handleCopy}
          icon={
            <Image
              source={icCopy}
              style={viewStyles.size10Icon}
              tintColor={Colors.Secondary300}
            />
          }
        />
      )}
    </View>
  )
}

const InvestorBasicItemView: React.FC<InvestorViewProps> = ({ investment }) => {
  const { t } = useTranslation()
  const { selectedRound, investmentRounds } = useInvestmentContext()
  const selectedInvestmentRound = investmentRounds.find(
    (round) => round.round === selectedRound
  )
  const tokenAllocation = BigInt(
    selectedInvestmentRound?.tokenAllocation || "0"
  )
  const { percentHolding, totalWithdrawn, totalAllocation } = useInvestor(
    tokenAllocation,
    investment
  )

  const handleCopy = async () => {
    if (!investment.address) return
    await Clipboard.setStringAsync(investment.address)
    showSuccess(t("Address copied"))
  }

  return (
    <View style={styles.basicContainer}>
      <ItemView
        icon={icCommonAvar}
        showBrikIcon={false}
        value={shortenAddress(investment.address)}
        valueColor={Colors.Secondary300}
        handleCopy={handleCopy}
      />
      <ItemView
        value={`${formatCurrency(formatEtherWithPrecision(totalAllocation))}`}
      />
      <ItemView
        value={`${formatCurrency(formatEtherWithPrecision(totalWithdrawn))}`}
      />
      <Text style={[styles.percentHolding, styles.textRight]}>
        {percentHolding}%
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  holder: {
    flexDirection: "row",
    alignItems: "center",
    flex: 2,
  },
  percentHolding: {
    ...textStyles.SSemiBold,
    flex: 1,
  },
  textCenter: {
    textAlign: "center",
  },
  value: {
    ...textStyles.SSemiBold,
    marginStart: 2,
  },
  textRight: {
    textAlign: "right",
  },
  basicContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  copy: {
    marginStart: 4,
  },
  brik: {
    ...viewStyles.size10Icon,
    marginStart: 2,
  },
})

export default InvestorBasicItemView
