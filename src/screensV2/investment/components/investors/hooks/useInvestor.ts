import { Investment } from "src/api/types/oracle"
import { PERCENTAGE_PRECISION_MULTIPLIER } from "../../../consts"

export const useInvestor = (
  tokenAllocation: bigint,
  investment: Investment
) => {
  const { distributions } = investment

  const filteredDistribution = (distributions || []).filter(
    (distribution) => !!distribution.distributionId
  )
  const totalAllocation = filteredDistribution.reduce((sum, distribution) => {
    return sum + BigInt(distribution.totalAmount || "0")
  }, 0n)

  const totalWithdrawn = filteredDistribution.reduce((sum, distribution) => {
    return sum + BigInt(distribution.withdrawnAmount || "0")
  }, 0n)
  const percentHolding =
    tokenAllocation > 0n
      ? Number(
          (totalAllocation * PERCENTAGE_PRECISION_MULTIPLIER) / tokenAllocation
        ) / 10000.0
      : 0

  return {
    totalAllocation,
    percentHolding,
    totalWithdrawn,
  }
}
