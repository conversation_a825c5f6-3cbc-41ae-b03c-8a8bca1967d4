import React from "react"
import { View, Text, StyleSheet, Image, ViewStyle } from "react-native"
import { useTranslation } from "react-i18next"
import { Investment } from "src/api"
import { useInvestmentContext } from "src/screensV2/investment/context/InvestmentContext"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import {
  formatCurrency,
  formatEtherWithPrecision,
  shortenAddress,
} from "src/utils"
import { showSuccess } from "utils/toast"
import * as Clipboard from "expo-clipboard"
import { IconButton } from "src/componentsv2/Button"
import { useInvestor } from "./hooks/useInvestor"
import icBrik from "assets/imagesV2/ic_brik.png"
import icSeedAvatar from "assets/imagesV2/ic_seed_avatar.png"
import icCopy from "assets/imagesV2/ic_copy.png"

interface InvestorViewProps {
  investment: Investment
}

interface RowItemViewProps {
  style?: ViewStyle
  label: string
  value: string
  showBrikIcon?: boolean
}

const RowItemView: React.FC<RowItemViewProps> = ({
  style,
  label,
  value,
  showBrikIcon = true,
}) => {
  return (
    <View style={[styles.rowContainer, style]}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.row}>
        <Text style={textStyles.SMedium}>{value}</Text>
        {showBrikIcon && <Image source={icBrik} style={styles.icon} />}
      </View>
    </View>
  )
}

const InvestorSeedRoundItemView: React.FC<InvestorViewProps> = ({
  investment,
}) => {
  const { t } = useTranslation()
  const { selectedRound, investmentRounds } = useInvestmentContext()
  const selectedInvestmentRound = investmentRounds.find(
    (round) => round.round === selectedRound
  )
  const tokenAllocation = BigInt(
    selectedInvestmentRound?.tokenAllocation || "0"
  )
  const { investorName, address, affiliation } = investment
  const { percentHolding, totalWithdrawn, totalAllocation } = useInvestor(
    tokenAllocation,
    investment
  )

  const handleCopy = async () => {
    if (!investment.address) return
    await Clipboard.setStringAsync(investment.address)
    showSuccess(t("Address copied"))
  }

  return (
    <View style={styles.container}>
      <View style={styles.rowSpaceBetween}>
        <View style={styles.row}>
          <Image source={icSeedAvatar} style={viewStyles.size10Icon} />
          <Text style={styles.address}>{shortenAddress(address)}</Text>
          <IconButton
            style={styles.copy}
            onPress={handleCopy}
            icon={
              <Image
                source={icCopy}
                style={viewStyles.size10Icon}
                tintColor={Colors.Secondary300}
              />
            }
          />
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>{t("Percent Holding")}</Text>
          <Text style={styles.percentHolding}>{percentHolding}%</Text>
        </View>
      </View>
      <RowItemView
        label={t("Representative")}
        value={investorName}
        showBrikIcon={false}
      />
      <RowItemView
        label={t("Office")}
        value={affiliation || "-"}
        showBrikIcon={false}
      />
      <View style={styles.row}>
        <RowItemView
          style={{ marginEnd: 6 }}
          label={t("Total Allocation")}
          value={formatCurrency(formatEtherWithPrecision(totalAllocation))}
        />
        <RowItemView
          label={t("Withdrawn")}
          value={formatCurrency(formatEtherWithPrecision(totalWithdrawn))}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
  },
  address: {
    ...textStyles.SSemiBold,
    color: Colors.Secondary300,
    marginStart: 4,
  },
  amount: {
    ...textStyles.MSemiBold,
    flex: 1,
    textAlign: "center",
    color: Colors.PalleteBlack,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  rowContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingHorizontal: 8,
    borderRadius: 4,
    paddingVertical: 6,
    marginBottom: 6,
    flex: 1,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  label: {
    ...textStyles.SMedium,
    color: Colors.Neutral500,
  },
  icon: {
    ...viewStyles.size10Icon,
    marginStart: 2,
  },
  percentHolding: {
    ...textStyles.SMedium,
    marginStart: 4,
  },
  copy: {
    marginStart: 4,
  },
})

export default InvestorSeedRoundItemView
