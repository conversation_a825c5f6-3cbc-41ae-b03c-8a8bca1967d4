import React, { createContext, useContext, ReactNode } from "react"
import { InvestmentContextState } from "./types"
import { useInvestmentProvider } from "src/screensV2/investment/hooks/useInvestmentProvider"

export const InvestmentContext = createContext<InvestmentContextState>({
  sections: [],
  isInvestmentLoading: false,
  investmentError: null,
  investmentRounds: [],
  description: "",
  selectedRound: undefined,
  roundNameMap: {},
  setSelectedRound: () => {},
})

export const useInvestmentContext = () => useContext(InvestmentContext)

interface InvestmentProviderProps {
  children: ReactNode
}
export const InvestmentProvider: React.FC<InvestmentProviderProps> = ({
  children,
}) => {
  const contextValue = useInvestmentProvider()

  return (
    <InvestmentContext.Provider value={contextValue}>
      {children}
    </InvestmentContext.Provider>
  )
}
