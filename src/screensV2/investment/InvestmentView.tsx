import React, { useCallback } from "react"
import { View, StyleSheet, FlatList, ListRenderItem } from "react-native"
import { useInvestmentContext } from "./context/InvestmentContext"
import { InvestmentSectionItem } from "./types"
import SectionRenderer from "./components/SectionRenderer"
import { Background, SimpleLoadingView } from "src/componentsv2"

const InvestmentView: React.FC = () => {
  const { sections, isInvestmentLoading } = useInvestmentContext()
  const renderItem: ListRenderItem<InvestmentSectionItem> = useCallback(
    ({ item }) => <SectionRenderer item={item} />,
    []
  )

  const keyExtractor = useCallback((item: InvestmentSectionItem) => item.id, [])

  return (
    <Background>
      <View style={styles.container}>
        {isInvestmentLoading ? (
          <SimpleLoadingView visible={true} />
        ) : (
          <FlatList
            data={sections}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainer}
          />
        )}
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 50,
  },
})

export default InvestmentView
