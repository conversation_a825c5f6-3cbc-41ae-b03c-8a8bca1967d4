import React from "react"
import { StyleSheet, Text, TextInput, View } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import { Controller, Control } from "react-hook-form"
import Colors from "src/config/colors"
import { SearchView } from "src/componentsv2/SearchView"
import { LabelView } from "src/componentsv2"
import { BROKER_SEARCH_TYPE, SELLER_SEARCH_TYPE } from "../hooks"

interface UserInfoSectionProps {
  control: Control<any>
  formState: any
  type: "seller" | "broker"
  searchWarning?: string | null
  userInfo?: { alias: string } | null
  onChangeSearchUser: (
    onChange: (value: string | number) => void,
    type: string
  ) => (text: string | number) => void | Promise<void>
}

const UserInfoSection: React.FC<UserInfoSectionProps> = ({
  control,
  formState,
  type,
  searchWarning,
  userInfo,
  onChangeSearchUser,
}) => {
  const { t } = useTranslation()
  const isSeller = type === "seller"
  const fieldName = isSeller ? "requesterAddress" : "brokerAddress"
  const searchType = isSeller ? SELLER_SEARCH_TYPE : BROKER_SEARCH_TYPE

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>
        {isSeller ? t("Seller Information") : t("Broker Information")}
      </Text>

      <View>
        <Controller
          control={control}
          name={fieldName}
          render={({ field: { onChange, value } }) => (
            <>
              <LabelView
                label={t("Wallet Address")}
                require={isSeller}
                style={{ marginBottom: 4 }}
              />
              <SearchView
                value={value}
                onChangeText={onChangeSearchUser(onChange, searchType)}
                multiline={false}
                error={
                  formState.errors[fieldName]?.message &&
                  String(formState.errors[fieldName]?.message)
                }
                placeholder={
                  isSeller
                    ? t("Input wallet address of the seller to search")
                    : t("Input wallet address of the broker to search")
                }
              />
            </>
          )}
        />
      </View>

      {searchWarning && (
        <Text style={styles.searchWarning}>{searchWarning}</Text>
      )}

      <View>
        <LabelView
          label={t("Full name")}
          require={false}
          style={{ marginBottom: 4 }}
        />
        <TextInput
          value={userInfo ? userInfo.alias : ""}
          editable={false}
          style={styles.textInputStyle}
          placeholder={
            isSeller ? t("Name of the broker") : t("Name of the seller")
          }
          placeholderTextColor={Colors.Neutral700}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  section: {
    width: "100%",
    gap: 8,
  },
  sectionTitle: {
    ...textStyles.LBold,
    marginBottom: 8,
  },
  searchWarning: {
    ...textStyles.MMedium,
    color: Colors.Warning500,
  },
  textInputStyle: {
    ...textStyles.MMedium,
    height: 38,
    width: "100%",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingHorizontal: 12,
  },
})

export default UserInfoSection
