import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import { Controller, Control } from "react-hook-form"
import { ErrorLabel } from "src/componentsv2/ErrorLabel"
import { LabelView } from "src/componentsv2/LabelView"
import { UploadImages } from "src/componentsv2/UploadImages"

interface PropertyImageSectionProps {
  control: Control<any>
  formState: any
}

const PropertyImageSection: React.FC<PropertyImageSectionProps> = ({
  control,
  formState,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{t("Property Images")}</Text>

      <Controller
        control={control}
        name="landUseRights"
        render={({ field: { onChange, value } }) => (
          <>
            <LabelView
              label={t("Land use rights images (minimum 2 images)")}
              require={true}
            />
            <UploadImages images={value} onChangeImages={onChange} />
            <ErrorLabel
              error={formState.errors.landUseRights?.message}
              style={{ marginTop: 0 }}
            />
          </>
        )}
      />

      <Controller
        control={control}
        name="thumnail"
        render={({ field: { onChange, value } }) => (
          <>
            <LabelView
              label={t("Thumbnail of the property (1 image)")}
              require={true}
            />
            <UploadImages
              images={value}
              isHideAddImageIcon={value.length > 0}
              onChangeImages={onChange}
              allowsMultipleSelection={false}
            />
            <ErrorLabel
              error={formState.errors.thumnail?.message}
              style={{ marginTop: 0 }}
            />
          </>
        )}
      />

      <Controller
        control={control}
        name="landMedia"
        render={({ field: { onChange, value } }) => (
          <>
            <LabelView
              label={t("Images of the property (minimum 4 images)")}
              require={true}
            />
            <UploadImages images={value} onChangeImages={onChange} />
            <ErrorLabel
              error={formState.errors.landMedia?.message}
              style={{ marginTop: 0 }}
            />
          </>
        )}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  section: {
    gap: 8,
  },
  sectionTitle: {
    ...textStyles.LBold,
    marginBottom: 8,
  },
})

export default PropertyImageSection
