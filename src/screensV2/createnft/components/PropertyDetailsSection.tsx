import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import { Controller, Control } from "react-hook-form"
import { Dropdown } from "react-native-element-dropdown"
import Colors from "src/config/colors"
import { CustomCheckbox } from "src/componentsv2/CustomCheckbox"
import { InputField } from "src/componentsv2/InputField"
import { LabelView, ErrorLabel } from "src/componentsv2"
import { DateTimeInput } from "src/componentsv2/DateTimeInput"
import AreaPicker from "src/componentsv2/areapicker"
import DropdownRenderItem from "src/componentsv2/DropdownRenderItem"
import { useAreaUnitOptions } from "src/screensV2/createnft/hooks"

interface PropertyDetailsSectionProps {
  control: Control<any>
  formState: any
  isInfinite: boolean
  location: {
    addressCodeLevel1: string
    addressCodeLevel2: string
    addressCodeLevel3: string
  }
  handleLocationChange: (addressLevel: string, value: string) => void
}

const PropertyDetailsSection: React.FC<PropertyDetailsSectionProps> = ({
  control,
  formState,
  isInfinite,
  location,
  handleLocationChange,
}) => {
  const { t } = useTranslation()
  const { areaUnitOptions } = useAreaUnitOptions()

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{t("Property Details")}</Text>

      <Controller
        control={control}
        name="name"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputField
            label={t("Name of the property")}
            value={value}
            require={true}
            onChangeText={onChange}
            onBlur={onBlur}
            placeholder={t("Name of the property")}
            error={
              formState.errors.name?.message &&
              String(formState.errors.name?.message)
            }
          />
        )}
      />

      <View style={styles.checkboxContainer}>
        <Controller
          control={control}
          name="isInfinite"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              label={t("Is Infinite")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
      </View>

      <View style={styles.row}>
        <View style={styles.flex1}>
          <Controller
            control={control}
            name="serial"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Serial Number")}
                value={value}
                onChangeText={onChange}
                require={true}
                onBlur={onBlur}
                placeholder={t("AB123456")}
                error={
                  formState.errors.serial?.message &&
                  String(formState.errors.serial?.message)
                }
              />
            )}
          />
        </View>

        <View style={styles.flex1}>
          <Controller
            control={control}
            name="expiredAt"
            render={({ field: { onChange, value } }) => (
              <>
                <DateTimeInput
                  style={{ marginTop: 0 }}
                  title={t("Date of expiry")}
                  value={value}
                  require={true}
                  disabled={isInfinite}
                  minimumDate={new Date()}
                  onChangeDate={(date) => {
                    onChange(date?.toDateString() || "")
                  }}
                />
                <ErrorLabel error={formState.errors.expiredAt?.message} />
              </>
            )}
          />
        </View>
      </View>

      <AreaPicker
        {...location}
        onChange={handleLocationChange}
        errorMessage={
          formState.errors.addressCodeLevel1?.message ||
          formState.errors.addressCodeLevel2?.message ||
          formState.errors.addressCodeLevel3?.message
        }
      />

      <Controller
        control={control}
        name="address"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputField
            label={t("Address")}
            value={value}
            require={true}
            onChangeText={onChange}
            onBlur={onBlur}
            placeholder={t("Example: No. 1, Nguyen Hue Street")}
            error={
              formState.errors.address?.message &&
              String(formState.errors.address?.message)
            }
          />
        )}
      />

      <View>
        <LabelView
          label={t("Area")}
          require={true}
          style={{ marginBottom: 4 }}
        />
        <View style={styles.row}>
          <Controller
            control={control}
            name="area"
            render={({ field: { onChange, onBlur, value } }) => (
              <View style={styles.flex1}>
                <InputField
                  value={value}
                  require={true}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  inputMode={"decimal"}
                  type={"number"}
                  error={
                    formState.errors.area?.message &&
                    String(formState.errors.area?.message)
                  }
                />
              </View>
            )}
          />
          <Controller
            control={control}
            name="areaUnit"
            render={({ field: { onChange, value } }) => (
              <Dropdown
                value={value}
                data={areaUnitOptions}
                labelField={"label"}
                valueField={"value"}
                onChange={({ value }) => onChange(value)}
                style={styles.dropdown}
                containerStyle={{ backgroundColor: Colors.Neutral900 }}
                selectedTextStyle={styles.dropdownItem}
                placeholderStyle={styles.dropdownItem}
                itemTextStyle={styles.dropdownItem}
                renderItem={DropdownRenderItem}
              />
            )}
          />
        </View>
      </View>

      <Controller
        control={control}
        name="description"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputField
            label={t("Property description")}
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            multiline={true}
            height={100}
            placeholder={t("Add the description for the property")}
            error={formState.errors.description?.message}
          />
        )}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  section: {
    gap: 8,
  },
  sectionTitle: {
    ...textStyles.LBold,
    marginBottom: 8,
  },
  row: {
    flexDirection: "row",
    gap: 8,
  },
  flex1: {
    flex: 1,
  },
  checkboxContainer: {
    alignSelf: "flex-end",
  },
  dropdown: {
    height: 38,
    width: 100,
    borderColor: Colors.Neutral900,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    ...textStyles.MMedium,
  },
  dropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
})

export default PropertyDetailsSection
