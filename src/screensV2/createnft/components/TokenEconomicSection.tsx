import React, { useState } from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import { Controller, Control, useWatch } from "react-hook-form"
import { Dropdown } from "react-native-element-dropdown"
import Colors from "src/config/colors"
import { InputField } from "src/componentsv2/InputField"
import { CustomPressable, LabelView } from "src/componentsv2"
import { SelectCurrency } from "src/componentsv2/SelectCurrency"
import DropdownRenderItem from "src/componentsv2/DropdownRenderItem"
import ChainlinkPriceFeedPopup from "src/screensV2/shared/components/ChainlinkPriceFeedPopup"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"

interface TokenEconomicSectionProps {
  control: Control<any>
  formState: any
  currencies: any[]
  durationUnitMap: Record<string, any>
  setValue: any
}

const TokenEconomicSection: React.FC<TokenEconomicSectionProps> = ({
  control,
  formState,
  currencies,
  durationUnitMap,
  setValue,
}) => {
  const { t } = useTranslation()

  const currencyId = useWatch({ control, name: "currencyId" })
  const unitPrice = useWatch({ control, name: "unitPrice" })
  const {
    tokenSymbol,
    rateValue,
    rateUpdatedAt,
    tokenDecimals,
    isShowChainlinkPriceFeed,
  } = useCurrencies(currencyId)
  const equivalentValue =
    rateValue !== "0"
      ? `${(Number(unitPrice) * 10 ** tokenDecimals) / Number(rateValue)} ${tokenSymbol}`
      : "0"

  const [isPopupPriceFeedVisible, setIsPopupPriceFeedVisible] = useState(false)

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{t("Token Economic")}</Text>

      <View>
        <View style={styles.row}>
          <View style={styles.flex1}>
            <Controller
              control={control}
              name="unitPrice"
              render={({ field: { onChange, onBlur, value } }) => (
                <InputField
                  label={t("USD Denomination")}
                  require={true}
                  value={value}
                  inputMode={"numeric"}
                  type={"number"}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={formState.errors.unitPrice?.message}
                  placeholder="0"
                />
              )}
            />
          </View>
          <SelectCurrency
            currencies={currencies}
            control={control}
            isShowMinMaxPrice={false}
            setValue={setValue}
            style={styles.flex1}
          />
        </View>

        {isShowChainlinkPriceFeed && (
          <CustomPressable
            style={styles.equivalentContainer}
            onPress={() => setIsPopupPriceFeedVisible(true)}
          >
            <View style={styles.equivalentContent}>
              <Text style={styles.equivalentTitle}>
                {t("Equivalent Value")}
              </Text>
              <Text style={styles.equivalentValue}>{equivalentValue}</Text>
            </View>
            <View style={styles.dashedUnderline} />
          </CustomPressable>
        )}

        {isPopupPriceFeedVisible && (
          <ChainlinkPriceFeedPopup
            isVisible={isPopupPriceFeedVisible}
            onClose={() => setIsPopupPriceFeedVisible(false)}
            chainlinkTimestamp={rateUpdatedAt}
          />
        )}
      </View>

      <View style={styles.row}>
        <Controller
          control={control}
          name="totalSupply"
          render={({ field: { onChange, value } }) => (
            <View style={styles.flex1}>
              <InputField
                label={t("Total supply")}
                value={value}
                require={true}
                type={"number"}
                inputMode={"decimal"}
                onChangeText={onChange}
                error={formState.errors.totalSupply?.message}
                placeholder="0"
              />
            </View>
          )}
        />
        <Controller
          control={control}
          name="decimals"
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={styles.flex1}>
              <InputField
                label={t("Decimal (maximum 18)")}
                require={true}
                value={value}
                type={"number"}
                inputMode={"numeric"}
                onChangeText={onChange}
                onBlur={onBlur}
                error={formState.errors.decimals?.message}
                placeholder="0"
              />
            </View>
          )}
        />
      </View>

      <View style={styles.row}>
        <Controller
          control={control}
          name="sellingLimit.minSellingAmount"
          render={({ field: { onChange, value } }) => (
            <View style={styles.flex1}>
              <InputField
                value={value}
                require={true}
                label={t("Minimum selling amount")}
                type={"number"}
                inputMode={"decimal"}
                onChangeText={onChange}
                error={formState.errors.sellingLimit?.minSellingAmount?.message}
                placeholder="0"
              />
            </View>
          )}
        />
        <Controller
          control={control}
          name="sellingLimit.maxSellingAmount"
          render={({ field: { onChange, value } }) => (
            <View style={styles.flex1}>
              <InputField
                value={value}
                require={true}
                label={t("Maximum selling amount")}
                type={"number"}
                inputMode={"decimal"}
                onChangeText={onChange}
                error={formState.errors.sellingLimit?.maxSellingAmount?.message}
                placeholder="0"
              />
            </View>
          )}
        />
      </View>

      <View>
        <LabelView
          label={t("Public Sale Duration")}
          require={true}
          style={{ marginBottom: 4 }}
        />

        <View style={styles.row}>
          <Controller
            control={control}
            name="duration"
            render={({ field: { onChange, value } }) => (
              <View style={styles.flex1}>
                <InputField
                  value={value}
                  require={true}
                  type={"number"}
                  inputMode={"decimal"}
                  onChangeText={onChange}
                  error={formState.errors.duration?.message}
                  placeholder="0"
                />
              </View>
            )}
          />
          <Controller
            control={control}
            name="durationUnit"
            render={({ field: { onChange, value } }) => (
              <Dropdown
                value={value}
                data={Object.keys(durationUnitMap).map((unit) => ({
                  label: t(unit),
                  value: unit,
                }))}
                labelField={"label"}
                valueField={"value"}
                onChange={({ value }) => onChange(value)}
                style={styles.durationDropdown}
                selectedTextStyle={styles.dropdownItem}
                placeholderStyle={styles.dropdownItem}
                itemTextStyle={styles.dropdownItem}
                renderItem={DropdownRenderItem}
              />
            )}
          />
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  section: {
    gap: 8,
  },
  sectionTitle: {
    ...textStyles.LBold,
    marginBottom: 8,
  },
  equivalentContainer: {
    marginTop: 6,
    alignSelf: "flex-start",
  },
  equivalentContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  equivalentTitle: {
    ...textStyles.MMedium,
    color: Colors.Neutral500,
  },
  equivalentValue: {
    ...textStyles.MSemiBold,
    color: Colors.Neutral500,
  },
  dashedUnderline: {
    borderWidth: 0.3,
    borderStyle: "dashed",
    borderColor: Colors.Neutral500,
    marginTop: 2,
  },
  row: {
    flexDirection: "row",
    gap: 8,
  },
  flex1: {
    flex: 1,
  },
  durationDropdown: {
    height: 38,
    width: "30%",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: "center",
  },
  dropdownItem: {
    ...textStyles.MMedium,
  },
})

export default TokenEconomicSection
