import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { createApplication } from "src/api"
import { NavigationProp, ParamListBase } from "@react-navigation/native"
import { useTranslation } from "react-i18next"
import { useHandleError } from "src/api/errors/handleError"
import { showSuccess } from "utils/toast"
import { parseEther } from "@ethersproject/units"
import { durationUnitMap } from "src/utils/timeExt"
export interface Image {
  url: string
  fileName: string
  type: string
}

export interface SellingLimit {
  minSellingAmount: number
  maxSellingAmount: number
}

export interface FormData {
  name: string
  serial: string
  address: string
  addressCodeLevel1: string
  addressCodeLevel2: string
  addressCodeLevel3: string
  area: number
  areaUnit: string
  description: string
  unitPrice: number
  currencyId: string
  sellingLimit: SellingLimit
  totalSupply: number
  landUseRights: Image[]
  landMedia: Image[]
  thumnail: Image[]
  decimals: number
  expiredAt: string
  isInfinite: boolean
  duration: number
  durationUnit: string
  brokerAddress: string
  requesterAddress: string
}

export const useCreateNFTMutation = (
  navigation: NavigationProp<ParamListBase>,
  userAddress?: string
) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const { handleError } = useHandleError()

  const mutationCreateNFT = useMutation({
    mutationFn: (formData: FormData) => {
      setIsLoading(true)
      const data = new FormData()
      return createApplication(prepareFormData(formData, data, userAddress))
    },
    onError: (error: any) => {
      setIsLoading(false)
      handleError(error, t("Request for tokenization failed"))
    },
    onSuccess: () => {
      setIsLoading(false)
      showSuccess(t("Request for tokenization success"))
      navigation.goBack()
    },
  })

  // Chuẩn bị FormData để gửi lên server
  const prepareFormData = (
    data: FormData,
    formData: any,
    userAddress?: string
  ) => {
    // TODO
    const zone = "VIETNAM"
    const zoneName = "Vietnam"
    const credentialType = "VN001"
    const credentialId = "AĐ12345678"
    const category = "RESIDENTIAL"

    const decimalsPower = BigInt(10) ** BigInt(data.decimals)
    formData.append("zone", zone)
    formData.append("zoneName", zoneName)
    formData.append("credentialType", credentialType)
    formData.append("credentialId", credentialId)
    formData.append(
      "attributes",
      JSON.stringify({ traitType: "Category", value: category })
    )

    formData.append("name", data.name)
    formData.append("address", data.address)
    formData.append("localeDetailVietnamLevel1", data.addressCodeLevel1)
    formData.append("localeDetailVietnamLevel2", data.addressCodeLevel2)
    formData.append("localeDetailVietnamLevel3", data.addressCodeLevel3)
    formData.append("description", data.description)
    formData.append(
      "area",
      JSON.stringify({ value: data.area.toString(), unit: data.areaUnit })
    )
    formData.append("category", category)
    formData.append("serial", data.serial)
    formData.append(
      "unitPrice",
      parseEther(data.unitPrice.toString()).toString()
    )
    formData.append("currency", data.currencyId)
    formData.append(
      "minSellingAmount",
      (BigInt(data.sellingLimit.minSellingAmount) * decimalsPower).toString()
    )
    formData.append(
      "maxSellingAmount",
      (BigInt(data.sellingLimit.maxSellingAmount) * decimalsPower).toString()
    )
    formData.append(
      "totalSupply",
      (BigInt(data.totalSupply) * decimalsPower).toString()
    )

    formData.append(
      "expireAt",
      data.isInfinite
        ? (Math.pow(2, 40) - 1).toString()
        : Math.floor(new Date(data.expiredAt!).getTime() / 1000).toString()
    )
    formData.append("decimals", data.decimals.toString())
    formData.append(
      "duration",
      (Number(data.duration) * durationUnitMap[data.durationUnit]).toString()
    )

    data.landUseRights.forEach((photo) => {
      // @ts-ignore
      formData.append("credentialPhotos", {
        uri: photo.url,
        type: photo.type,
        name: photo.fileName,
      })
    })

    data.landMedia.forEach((photo) => {
      // @ts-ignore
      formData.append("estatePhotos", {
        uri: photo.url,
        type: photo.type,
        name: photo.fileName,
      })
    })

    data.thumnail.forEach((photo) => {
      // @ts-ignore
      formData.append("image", {
        uri: photo.url,
        type: photo.type,
        name: photo.fileName,
      })
    })

    formData.append("brokerAddress", data.brokerAddress)

    if (userAddress && !data.requesterAddress) {
      formData.append("requesterAddress", userAddress)
    } else {
      formData.append("requesterAddress", data.requesterAddress)
    }

    return formData
  }

  const onSubmit = (data: FormData) => {
    if (isLoading) return
    mutationCreateNFT.mutate(data)
  }

  return {
    isLoading,
    onSubmit,
  }
}
