import { useState } from "react"
import { User, getMyProfile } from "src/api"
import { useMutation } from "@tanstack/react-query"
import { useTranslation } from "react-i18next"
import { useHandleError } from "src/api/errors/handleError"
import { isValidWalletAddress } from "utils"

export const BROKER_SEARCH_TYPE = "broker"
export const SELLER_SEARCH_TYPE = "seller"

export const useUserSearch = (profile: User | null) => {
  const { t } = useTranslation()
  const { handleError } = useHandleError()
  const [isLoading, setIsLoading] = useState(false)

  // Broker state
  const [brokerInfo, setBrokerInfo] = useState<User | null>(null)
  const [searchBrokerWarning, setSearchBrokerWarning] = useState<string | null>(
    null
  )

  // Seller state
  const [sellerInfo, setSellerInfo] = useState<User | null>(null)
  const [searchSellerWarning, setSearchSellerWarning] = useState<string | null>(
    null
  )

  // Tạo mutation chung để tìm kiếm user
  const useSearchUserMutation = (
    onSuccessCallback: (profile: User) => void,
    onErrorCallback: () => void
  ) => {
    return useMutation({
      mutationFn: (walletAddress: string) => {
        setIsLoading(true)
        return getMyProfile(walletAddress)
      },
      onError: (error: any) => {
        handleError(error)
        onErrorCallback()
      },
      onSuccess: (profile: User) => {
        onSuccessCallback(profile)
      },
      onSettled: () => {
        setIsLoading(false)
      },
    })
  }

  // Tạo mutation cho Broker
  const mutationGetBrokerInfo = useSearchUserMutation(
    (brokerProfile) => {
      if (brokerProfile.address === profile?.address) {
        setSearchBrokerWarning(
          t("This wallet address is the same as your wallet address")
        )
        setBrokerInfo(null)
      } else if (brokerProfile.isBroker) {
        setSearchBrokerWarning("")
        setBrokerInfo(brokerProfile)
      } else {
        setSearchBrokerWarning(t("Wallet address not found"))
        setBrokerInfo(null)
      }
    },
    () => {
      setSearchBrokerWarning(t("Wallet address not found"))
      setBrokerInfo(null)
    }
  )

  // Tạo mutation cho Seller
  const mutationGetSellerInfo = useSearchUserMutation(
    (sellerProfile) => {
      setSearchSellerWarning("")
      setSellerInfo(sellerProfile)
    },
    () => {
      setSearchSellerWarning(t("Wallet address not found"))
      setSellerInfo(null)
    }
  )

  // Hàm xử lý thay đổi trong ô tìm kiếm
  const onChangeSearchUser =
    (onChange: (value: string | number) => void, type: string) =>
    async (text: string | number) => {
      const walletAddress = String(text)
      onChange(walletAddress)

      if (isValidWalletAddress(walletAddress)) {
        if (type === BROKER_SEARCH_TYPE) {
          mutationGetBrokerInfo.mutate(walletAddress)
        } else if (type === SELLER_SEARCH_TYPE) {
          mutationGetSellerInfo.mutate(walletAddress)
        }
      } else {
        if (type === BROKER_SEARCH_TYPE) {
          setSearchBrokerWarning("")
          setBrokerInfo(null)
        } else if (type === SELLER_SEARCH_TYPE) {
          setSearchSellerWarning("")
          setSellerInfo(null)
        }
      }
    }

  return {
    isLoading,
    brokerInfo,
    searchBrokerWarning,
    sellerInfo,
    searchSellerWarning,
    onChangeSearchUser,
  }
}
