import { NavigationProp, useNavigation } from "@react-navigation/native"
import { RootStackParamList } from "src/navigatorV2/rootStackParamsList"
import { ESTATE_TAB, LIST_LANDS } from "src/navigatorV2/routes/RoutesV2"

interface UseTokenizedReturn {
  handleExplore: () => void
}

export function useTokenized(): UseTokenizedReturn {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>()

  const handleExplore = () => {
    //Index 2 (Tokenized tab)
    navigation.navigate(ESTATE_TAB, {
      screen: LIST_LANDS,
      params: { tabIndex: 2 },
    })
  }

  return {
    handleExplore,
  }
}
