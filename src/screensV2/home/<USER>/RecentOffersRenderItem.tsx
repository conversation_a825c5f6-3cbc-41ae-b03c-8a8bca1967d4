import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { MarketplaceOffer } from "src/api/types/marketplace"
import { CardView } from "src/componentsv2"
import { formatCurrencyByDecimals, fixedPointMultiply } from "src/utils/format"
import { useTranslation } from "react-i18next"
import iconYes from "assets/imagesV2/ic_circle_check.png"
import iconNo from "assets/imagesV2/ic_circle_x.png"
import { AddressView } from "src/componentsv2"
import { useCurrencies } from "../../shared/hooks/useCurrencies"
import { OfferActionButton } from "src/screensV2/shared/components/OfferActionButton"
import OfferState from "src/screensV2/shared/components/OfferState"

interface RecentOffersRenderItemProps {
  offer: MarketplaceOffer
}

const RecentOffersRenderItem: React.FC<RecentOffersRenderItemProps> = ({
  offer,
}) => {
  const { t } = useTranslation()

  if (!offer) {
    return null
  }

  const {
    estate: {
      metadata: { metadata: { name = "", image = "" } = {} } = {},
      decimals: estateDecimals = 0,
    } = {},
    unitPrice = "0",
    sellingAmount = "0",
    soldAmount = "0",
    isDivisible = false,
    state = "",
    seller: { address = "" } = {},
    currency = "",
  } = offer

  const { tokenSymbol } = useCurrencies(currency)

  const formattedUnitPrice = formatCurrencyByDecimals(unitPrice, estateDecimals)
  const formattedSellingAmount = formatCurrencyByDecimals(
    sellingAmount,
    estateDecimals
  )

  const totalPrice = fixedPointMultiply(
    BigInt(unitPrice),
    BigInt(sellingAmount),
    estateDecimals
  )
  const displayTotalPrice = `${formatCurrencyByDecimals(
    totalPrice.toString(),
    estateDecimals
  )} ${tokenSymbol || ""}`

  const displayPricePerNFT = `${formattedUnitPrice} ${tokenSymbol || ""}`
  const displaySoldAmount = `${formatCurrencyByDecimals(
    soldAmount,
    estateDecimals
  )} / ${formattedSellingAmount}`

  return (
    <CardView style={styles.container}>
      <View style={styles.realEstateContainer}>
        <Image source={{ uri: image }} style={styles.estateImage} />
        <View style={styles.nameContainer}>
          <Text style={styles.nameText} numberOfLines={1} ellipsizeMode="tail">
            {name}
          </Text>
        </View>
      </View>

      <Text style={[styles.columnText, textStyles.SBold]}>
        {displayPricePerNFT}
      </Text>
      <Text style={[styles.columnText, textStyles.SBold]}>
        {displayTotalPrice}
      </Text>

      <Text style={styles.columnText}>{displaySoldAmount}</Text>

      <View style={styles.yesNoContainer}>
        <Image
          source={isDivisible ? iconYes : iconNo}
          style={styles.yesNoIcon}
        />
        <Text style={styles.yesNoText}>{isDivisible ? t("Yes") : t("No")}</Text>
      </View>

      <View style={styles.column}>
        <OfferState state={state} style={styles.statusText} />
      </View>

      <AddressView style={styles.column} address={address} />

      <View style={styles.buyButtonContainer}>
        <OfferActionButton
          offer={offer}
          buttonWidth={70}
          buttonHeight={25}
          borderRadius={4}
        />
      </View>
    </CardView>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    padding: 8,
  },
  realEstateContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: 140,
    marginRight: 8,
  },
  estateImage: {
    ...viewStyles.size20Icon,
    borderRadius: 2,
    marginRight: 8,
  },
  nameContainer: {
    flex: 1,
  },
  nameText: {
    ...textStyles.SMedium,
    color: textColors.textWhite,
  },
  column: {
    width: 80,
    marginRight: 8,
  },
  columnText: {
    ...textStyles.SMedium,
    color: textColors.textWhite,
    width: 80,
    marginRight: 8,
  },
  yesNoContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: 80,
    marginRight: 8,
  },
  yesNoIcon: {
    ...viewStyles.size12Icon,
    marginRight: 4,
  },
  yesNoText: {
    ...textStyles.SMedium,
    color: textColors.textWhite,
  },
  statusText: {
    ...textStyles.SMedium,
    padding: 6,
    borderRadius: 16,
    alignSelf: "center",
  },
  buyButtonContainer: {
    width: 80,
    alignItems: "flex-end",
  },
})

export default RecentOffersRenderItem
