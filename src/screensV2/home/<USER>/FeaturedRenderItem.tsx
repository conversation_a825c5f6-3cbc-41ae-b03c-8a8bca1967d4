import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { Estate, EstateZone, EstateTokenAreaUnit } from "src/api/types/estate"
import { AreaUnitView, CardView, ExpandView } from "src/componentsv2"
import { formatCurrencyByDecimals, formatCurrency } from "src/utils/format"
import { InfoItem } from "src/screensV2/shared/components"
import fileBoxIcon from "assets/imagesV2/ic_file_box.png"
import mapPinIcon from "assets/imagesV2/ic_map_pin.png"
import scalingIcon from "assets/imagesV2/ic_scaling.png"
import { shortenAddress } from "utils/stringExt"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import * as Routes from "src/navigatorV2/routes/RoutesV2"
import { useCurrencies } from "../../shared/hooks/useCurrencies"
import { CustomPressable } from "src/componentsv2/CustomPressable"

interface FeaturedRenderItemProps {
  estate: Estate
  width: number
}

const FeaturedRenderItem: React.FC<FeaturedRenderItemProps> = ({
  estate,
  width,
}) => {
  const { t } = useTranslation()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const {
    id = "",
    metadata: {
      metadata: {
        name = "",
        description = "",
        area = { area: 0, unit: EstateTokenAreaUnit.SQM },
        image = "",
        locale_detail = { zone: EstateZone.VIETNAM },
      } = {},
      estatePhotoUrls = [],
    } = {},
    tokenizationRequest: {
      totalSupply = "0",
      soldAmount = "0",
      unitPrice = "0",
      maxSellingAmount = "0",
      decimals = 0,
      currency = "",
      requester: { address = "", avatarUrl = "" } = {},
    } = {},
  } = estate || {}

  const mainImageUrl = estatePhotoUrls[0] || image || ""
  const zone = locale_detail.zone || EstateZone.VIETNAM

  const handlePress = () => {
    navigation.navigate(Routes.ESTATE_DETAIL, { estateId: id })
  }

  const availableAmount = Number(maxSellingAmount) - Number(soldAmount)

  const unitPriceFormatted = formatCurrencyByDecimals(unitPrice, decimals)

  const { tokenSymbol } = useCurrencies(currency)

  const requesterAddressShortened = shortenAddress(address)

  return (
    <CustomPressable
      style={[styles.slideItem, { width }]}
      onPress={handlePress}
    >
      <Image source={{ uri: mainImageUrl }} style={styles.propertyImage} />

      <View style={styles.contentContainer}>
        <Text style={styles.title} numberOfLines={2}>
          {name}
        </Text>

        <View style={styles.creatorContainer}>
          <Text style={styles.byText}>{t("By")}</Text>
          {avatarUrl && (
            <Image
              source={{
                uri: avatarUrl,
              }}
              style={styles.avatar}
            />
          )}
          <Text style={styles.creatorName}>{requesterAddressShortened}</Text>
        </View>

        <Text style={styles.description} numberOfLines={2}>
          {description}
        </Text>

        <ExpandView />

        <CardView style={styles.infoCard}>
          <Text style={styles.availableText}>
            {t("Available")} {formatCurrency(availableAmount)} {t("NFTs")}{" "}
            {t("at price")} {unitPriceFormatted} {tokenSymbol}
          </Text>

          <View style={styles.infoRow}>
            <InfoItem
              icon={fileBoxIcon}
              iconStyle={styles.infoItemIcon}
              text={`${formatCurrency(totalSupply)} ${t("NFT")}`}
            />
            <Text style={styles.dot}>•</Text>
            <InfoItem
              icon={mapPinIcon}
              iconStyle={styles.infoItemIcon}
              text={zone}
            />
            <Text style={styles.dot}>•</Text>
            <View style={styles.areaContainer}>
              <InfoItem
                icon={scalingIcon}
                iconStyle={styles.infoItemIcon}
                text={""}
              />
              {area && area.unit && (
                <AreaUnitView area={area.area} areaUnit={area.unit} />
              )}
            </View>
          </View>
        </CardView>
      </View>
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  slideItem: {
    backgroundColor: Colors.opacityBlack20,
    flex: 1,
  },
  propertyImage: {
    width: "100%",
    height: 210,
    borderRadius: 6,
    resizeMode: "cover",
    marginBottom: 16,
  },
  contentContainer: {
    width: "100%",
    flex: 1,
  },
  title: {
    ...textStyles.size2XLMedium,
    color: Colors.white,
    marginBottom: 8,
  },
  creatorContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  byText: {
    ...textStyles.MMedium,
    color: Colors.white,
    marginRight: 6,
  },
  avatar: {
    ...viewStyles.size14Icon,
    resizeMode: "cover",
    marginRight: 6,
  },
  creatorName: {
    ...textStyles.MBold,
    color: Colors.white,
  },
  description: {
    ...textStyles.SMedium,
    color: Colors.white,
    marginBottom: 16,
  },
  infoCard: {
    marginBottom: 16,
    padding: 8,
  },
  infoItemIcon: {
    tintColor: Colors.PalleteWhite,
    ...viewStyles.size12Icon,
  },
  infoItemText: {
    ...textStyles.MMedium,
  },
  availableText: {
    ...textStyles.LMedium,
    color: Colors.white,
    marginBottom: 8,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  areaContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  dot: {
    color: Colors.white,
    marginHorizontal: 3,
  },
})

export default FeaturedRenderItem
