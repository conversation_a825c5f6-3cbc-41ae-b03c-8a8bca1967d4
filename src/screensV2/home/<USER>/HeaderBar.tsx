import React from "react"
import { StyleSheet, Text, View } from "react-native"
import Colors from "src/config/colors"
import { CustomPressable, ExpandView } from "src/componentsv2"
import { textStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"

interface HeaderBarProps {
  title: string
  isShowExplore?: boolean
  onPressExplore?: () => void
}

const HeaderBar: React.FC<HeaderBarProps> = ({
  title,
  isShowExplore = true,
  onPressExplore = () => {},
}) => {
  const { t } = useTranslation()
  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>{t(title)}</Text>

      <ExpandView />

      {isShowExplore && (
        <CustomPressable onPress={onPressExplore}>
          <Text style={styles.exploreText}>{t("Explore")}</Text>
        </CustomPressable>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 16,
  },
  sectionTitle: {
    ...textStyles.size2XLMedium,
    color: Colors.white,
  },
  exploreText: {
    ...textStyles.MMedium,
    color: Colors.white,
  },
})

export default HeaderBar
