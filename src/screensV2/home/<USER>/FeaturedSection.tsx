import React, { useEffect, useRef } from "react"
import { Animated, Dimensions, StyleSheet, View } from "react-native"

import { Estate } from "src/api/types/estate"
import Colors from "src/config/colors"
import AutoProgressBar from "./AutoProgressBar"
import FeaturedRenderItem from "./FeaturedRenderItem"
import { useFeatured } from "./hooks/useFeatured"

interface FeaturedSectionProps {
  featuredEstates: Estate[]
}

const { width: SCREEN_WIDTH } = Dimensions.get("window")
const VIEW_WIDTH = SCREEN_WIDTH - 64

const FeaturedSection: React.FC<FeaturedSectionProps> = ({
  featuredEstates,
}) => {
  if (featuredEstates.length === 0) {
    return null
  }
  const { flatListRef, activeIndex, handleScroll } = useFeatured(VIEW_WIDTH)

  const SCROLL_INTERVAL = 3500
  const progressAnims = useRef(
    featuredEstates.map(() => new Animated.Value(0))
  ).current

  useEffect(() => {
    const autoScroll = setInterval(() => {
      if (flatListRef.current) {
        const nextIndex = (activeIndex + 1) % featuredEstates.length
        flatListRef.current.scrollTo({
          x: nextIndex * VIEW_WIDTH,
          animated: true,
        })
      }
    }, SCROLL_INTERVAL)

    return () => clearInterval(autoScroll)
  }, [activeIndex, featuredEstates.length])

  useEffect(() => {
    // Reset giá trị các segment
    progressAnims.forEach((anim, index) => {
      if (index >= activeIndex) {
        anim.setValue(0)
      } else {
        anim.setValue(1)
      }
    })

    // Animate segment hiện tại
    Animated.timing(progressAnims[activeIndex], {
      toValue: 1,
      duration: SCROLL_INTERVAL,
      useNativeDriver: false,
    }).start()
  }, [activeIndex])

  // Render một item trong slider
  const renderItem = (item: Estate) => {
    return <FeaturedRenderItem key={item.id} estate={item} width={VIEW_WIDTH} />
  }

  return (
    <View style={styles.container}>
      <Animated.ScrollView
        ref={flatListRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        decelerationRate="fast"
        snapToInterval={VIEW_WIDTH}
        snapToAlignment="center"
      >
        {featuredEstates.map(renderItem)}
      </Animated.ScrollView>

      <View style={styles.progressTrack}>
        {featuredEstates.map((item, idx) => (
          <AutoProgressBar key={item.id} progress={progressAnims[idx]} />
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.opacityBlack20,
    padding: 16,
  },
  progressTrack: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
})

export default FeaturedSection
