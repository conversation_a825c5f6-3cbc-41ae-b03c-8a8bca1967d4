import React, { useMemo } from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { TokenizationRequest } from "src/api/types/estate"
import { CustomPressable } from "src/componentsv2/CustomPressable"
import { useTranslation } from "react-i18next"
import boxIcon from "assets/imagesV2/ic_box.png"
import fileBoxIcon from "assets/imagesV2/ic_file_box.png"
import mapPinIcon from "assets/imagesV2/ic_map_pin.png"
import scalingIcon from "assets/imagesV2/ic_scaling.png"
import { AreaUnitView, CardView, CircularProgress } from "src/componentsv2"
import InfoItem from "../../shared/components/InfoItem"
import {
  formatCurrencyByDecimals,
  formatCurrency,
  formatNumericByDecimals,
} from "src/utils/format"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"
import { useNavigateRequestDetail } from "src/screensV2/shared/hooks/useNavigateRequestDetail"
import { EstateZone, EstateTokenAreaUnit } from "src/api/types/estate"

interface HotDealRenderItemProps {
  tokenizationRequest: TokenizationRequest
}

const HotDealRenderItem: React.FC<HotDealRenderItemProps> = ({
  tokenizationRequest,
}) => {
  const { t } = useTranslation()

  const {
    metadata: {
      metadata: {
        name = "",
        locale_detail = { zone: EstateZone.VIETNAM },
        area = { area: 0, unit: EstateTokenAreaUnit.SQM },
      } = {},
      imageUrl = "",
    } = {},
    unitPrice = "0",
    soldAmount = "0",
    maxSellingAmount = "0",
    totalSupply = "0",
    currency = "",
    decimals = 0,
  } = tokenizationRequest || {}

  const displayProgressPercentage = useMemo(() => {
    if (!soldAmount || !maxSellingAmount) return 0
    return Math.floor((Number(soldAmount) * 100) / Number(maxSellingAmount))
  }, [soldAmount, maxSellingAmount])

  const formattedUnitPrice = formatCurrencyByDecimals(unitPrice, decimals)

  const { tokenSymbol } = useCurrencies(currency)
  const displayUnitPrice = `${formattedUnitPrice} ${tokenSymbol} / NFT`
  const displayTotalSupply = `${formatCurrency(totalSupply)} NFT`

  const amountRaised = formatCurrency(
    Number(formatNumericByDecimals(unitPrice, decimals)) * Number(soldAmount)
  )

  const displayAmountRaised = `${amountRaised} ${tokenSymbol}`

  const handleNavigate = useNavigateRequestDetail({ tokenizationRequest })

  return (
    <CardView style={styles.card}>
      <CustomPressable style={styles.container} onPress={handleNavigate}>
        <Image source={{ uri: imageUrl }} style={styles.image} />

        <View style={styles.contentContainer}>
          <Text style={styles.title} numberOfLines={1}>
            {name}
          </Text>

          <View style={styles.raisedRow}>
            <Text style={styles.raisedLabel}>
              {t("Raised")} {displayAmountRaised}
            </Text>
            <Text style={styles.dot}>•</Text>
            <CircularProgress percentage={displayProgressPercentage} />
            <Text style={styles.percentageText}>
              {displayProgressPercentage}%
            </Text>
          </View>

          <View>
            <View style={styles.infoRow}>
              <InfoItem
                icon={boxIcon}
                iconStyle={styles.infoItemIcon}
                text={displayUnitPrice}
              />
              <Text style={styles.dot}>•</Text>
              <InfoItem
                icon={fileBoxIcon}
                iconStyle={styles.infoItemIcon}
                text={displayTotalSupply}
              />
            </View>

            <View style={styles.infoRow}>
              <InfoItem
                icon={mapPinIcon}
                iconStyle={styles.infoItemIcon}
                text={locale_detail.zone}
              />
              <Text style={styles.dot}>•</Text>
              <View style={styles.areaContainer}>
                <InfoItem
                  icon={scalingIcon}
                  iconStyle={styles.infoItemIcon}
                  text={""}
                />
                <AreaUnitView area={area.area} areaUnit={area.unit} />
              </View>
            </View>
          </View>
        </View>
      </CustomPressable>
    </CardView>
  )
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    padding: 8,
  },
  container: {
    width: "100%",
    flexDirection: "row",
  },
  image: {
    borderRadius: 4,
    resizeMode: "cover",
    width: 100,
    marginEnd: 8,
  },
  contentContainer: {
    justifyContent: "space-between",
    flex: 1,
  },
  title: {
    ...textStyles.MMedium,
    color: Colors.white,
  },
  raisedRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  raisedLabel: {
    ...textStyles.SMedium,
    color: Colors.white,
  },
  percentageText: {
    ...textStyles.SMedium,
    color: Colors.white,
    marginStart: 4,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoItemIcon: {
    tintColor: Colors.PalleteWhite,
  },
  dot: {
    color: Colors.white,
    marginHorizontal: 4,
  },
  areaContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
})

export default HotDealRenderItem
