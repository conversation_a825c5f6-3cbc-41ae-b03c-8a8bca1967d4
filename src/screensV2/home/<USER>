import React from "react"
import { FlatList, StyleSheet, View } from "react-native"
import { Background } from "src/componentsv2"
import FeaturedSection from "./components/FeaturedSection"
import HotDealsSection from "./components/HotDealsSection"
import SaleLiveSection from "./components/SaleLiveSection"
import UpcomingSection from "./components/UpcomingSection"
import TokenizedSection from "./components/TokenizedSection"
import RecentOffersSection from "./components/RecentOffersSection"
import { Section } from "./hooks/useHomeData"
import { useHomeContext } from "./context/HomeContext"

const HomeView: React.FC = () => {
  const { sections } = useHomeContext()

  const renderSectionContent = (section: Section) => {
    switch (section.type) {
      case "featuredEstates":
        return <FeaturedSection featuredEstates={section.data} />
      case "hotDeals":
        return <HotDealsSection hotDeals={section.data} title={section.title} />
      case "saleLive":
        return <SaleLiveSection saleLive={section.data} title={section.title} />
      case "upcoming":
        return (
          <UpcomingSection
            upcomingRequests={section.data}
            title={section.title}
          />
        )
      case "tokenized":
        return (
          <TokenizedSection
            tokenizedEstates={section.data}
            title={section.title}
          />
        )
      case "recentOffers":
        return (
          <RecentOffersSection
            recentOffers={section.data}
            title={section.title}
          />
        )
      default:
        return null
    }
  }

  return (
    <Background>
      <View style={styles.container}>
        <FlatList
          data={sections}
          keyExtractor={(_, index) => `${index}`}
          renderItem={({ item }) => renderSectionContent(item)}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 16,
  },
})

export default HomeView
