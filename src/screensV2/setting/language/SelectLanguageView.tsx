import React, { useCallback } from "react"
import { Image, StyleSheet, Text, View, FlatList } from "react-native"
import { Background, CustomPressable, TopBar } from "src/componentsv2"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { useTranslation } from "react-i18next"
import { Language } from "../types"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { showError } from "utils/toast"
import { saveLanguage } from "src/store/LocalAsyncStore"
import icCheck from "assets/imagesV2/ic_check.png"

interface LanguageItemViewProps {
  selectedLanguage: string
  title: string
  language: string
  onPress: (language: string) => void
}

type LanguageItem = {
  title: string
  language: string
}

const LanguageItemView: React.FC<LanguageItemViewProps> = ({
  title,
  selectedLanguage,
  language,
  onPress,
}) => {
  const isSelectedLanguage = selectedLanguage === language

  return (
    <CustomPressable onPress={() => onPress(language)}>
      <View style={styles.row}>
        <Text
          style={[
            textStyles.LMedium,
            {
              color: isSelectedLanguage
                ? Colors.PalleteWhite
                : Colors.Neutral500,
            },
          ]}
        >
          {title}
        </Text>
        {isSelectedLanguage && (
          <Image source={icCheck} style={viewStyles.size16Icon} />
        )}
      </View>
    </CustomPressable>
  )
}

interface SelectLanguageViewProps {
  selectedLanguage: string
}

const SelectLanguageView: React.FC<SelectLanguageViewProps> = ({
  selectedLanguage,
}) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const { t, i18n } = useTranslation()

  const onGoBack = () => {
    navigation.goBack()
  }

  const selectLanguage = (language: string) => {
    saveLanguage(language)
    i18n.changeLanguage(language).then(
      () => {
        onGoBack()
      },
      (err) => {
        showError(err)
      }
    )
  }

  const languages = [
    {
      title: t("English"),
      language: Language.en,
    },
    {
      title: t("Tiếng Việt"),
      language: Language.vi,
    },
  ].sort((a, b) =>
    a.language === selectedLanguage
      ? -1
      : b.language === selectedLanguage
        ? 1
        : 0
  )

  const renderItem = useCallback(
    (item: LanguageItem) => (
      <LanguageItemView
        key={item.language}
        title={item.title}
        language={item.language}
        onPress={selectLanguage}
        selectedLanguage={selectedLanguage}
      />
    ),
    []
  )

  return (
    <Background>
      <TopBar enableBack={true} title={t("Language")} />
      <View style={styles.container}>
        <FlatList
          style={styles.list}
          scrollEnabled={false}
          data={languages}
          renderItem={({ item }) => renderItem(item)}
          keyExtractor={(_, index) => index.toString()}
          numColumns={1}
        />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  list: {
    marginTop: 16,
  },
  row: {
    paddingVertical: 12,
    marginTop: 8,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  divider: {
    height: 1,
    marginBottom: 16,
    width: "100%",
    backgroundColor: Colors.Neutral900,
  },
})

export { SelectLanguageView }
