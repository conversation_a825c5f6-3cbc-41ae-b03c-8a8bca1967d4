import React from "react"
import { RouteProp } from "@react-navigation/native"
import { SelectLanguageView } from "./SelectLanguageView"
import { RootStackParamList } from "src/navigatorV2"

interface SelectLanguageScreenProps {
  route: RouteProp<RootStackParamList, "SelectLanguage">
}

const SelectLanguageScreen: React.FC<SelectLanguageScreenProps> = ({
  route,
}) => {
  const { selectedLanguage } = route.params
  return <SelectLanguageView selectedLanguage={selectedLanguage} />
}

export default SelectLanguageScreen
