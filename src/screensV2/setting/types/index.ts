export enum Language {
  vi = "vi",
  en = "en",
}

export const LANGUAGE_LABELS: Record<string, string> = {
  en: "English",
  vi: "Tiếng Việt",
} as const

export const getLanguageLabel = (language: string): string => {
  return LANGUAGE_LABELS[language as Language] || language
}

export type Branch = {
  headOfficeName: string
  address: string
}

export type Office = {
  nationalName: string
  companyName: string
  branches?: Branch[]
}
