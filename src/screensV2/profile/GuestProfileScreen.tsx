import React, { useState, useCallback } from "react"
import ProfileView from "./ProfileView"
import { ProfileContext } from "./context/ProfileContext"
import { useRoute, RouteProp, useFocusEffect } from "@react-navigation/native"
import { RootStackParamList } from "src/navigatorV2/rootStackParamsList"
import { GUEST_PROFILE } from "src/navigatorV2"
import { useQuery } from "@tanstack/react-query"
import { getMyProfile } from "src/api"
import { TopBar } from "src/componentsv2"
import { useTranslation } from "react-i18next"
import { useAccount } from "wagmi"

type GuestProfileRouteProps = RouteProp<
  RootStackParamList,
  typeof GUEST_PROFILE
> & {
  params: {
    address?: string
  }
}

const GuestProfileScreen: React.FC = () => {
  const { t } = useTranslation()
  // Quản lý tab index
  const [activeTabIndex, setActiveTabIndex] = useState(0)
  const { address } = useAccount()
  const route = useRoute<GuestProfileRouteProps>()
  const walletAddress = route.params?.address
  const {
    data: profile = null,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ["profile"],
    queryFn: () => getMyProfile(walletAddress),
  })

  useFocusEffect(
    useCallback(() => {
      refetch()
    }, [refetch])
  )

  const contextValue = {
    address: walletAddress,
    profile,
    isLoading: isFetching,
    activeTabIndex,
    setActiveTabIndex,
    isGuest: address?.toLowerCase() !== walletAddress?.toLowerCase(),
  }

  return (
    <ProfileContext.Provider value={contextValue}>
      <>
        <TopBar title={t("Profile")} enableBack={true} />
        <ProfileView />
      </>
    </ProfileContext.Provider>
  )
}

export default GuestProfileScreen
