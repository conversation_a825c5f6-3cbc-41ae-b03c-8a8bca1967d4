import React, { useState } from "react"
import ProfileView from "./ProfileView"
import { ProfileContext } from "./context/ProfileContext"
import { useAtom } from "jotai"
import { profileAtom } from "../../context/AuthContext"
import { useAccount } from "wagmi"

const ProfileScreen: React.FC = () => {
  const [activeTabIndex, setActiveTabIndex] = useState(0)
  const { address } = useAccount()
  const [profile] = useAtom(profileAtom)

  const contextValue = {
    address,
    profile,
    isLoading: false,
    activeTabIndex,
    setActiveTabIndex,
    isGuest: false,
  }

  return (
    <ProfileContext.Provider value={contextValue}>
      <ProfileView />
    </ProfileContext.Provider>
  )
}

export default ProfileScreen
