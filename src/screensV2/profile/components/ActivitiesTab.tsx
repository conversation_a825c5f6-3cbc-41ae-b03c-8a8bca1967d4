import React from "react"
import { StyleSheet, View, ActivityIndicator } from "react-native"
import Colors from "src/config/colors"
import { useProfileContext } from "../context/ProfileContext"
import ActivityItem from "../../estateDetail/components/components/tabs/ActivityItem"
import { EstateActivity, getUserActivities } from "src/api"
import SimplePagingList from "src/componentsv2/simplepaginglist/SimplePagingList"

const ActivitiesTab: React.FC = () => {
  const { isLoading, address } = useProfileContext()

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.Primary500} />
      </View>
    )
  }

  const renderItem = (item: EstateActivity) => (
    <ActivityItem activity={item} decimals={18} />
  )

  return (
    <View style={styles.container}>
      <SimplePagingList<EstateActivity>
        getData={(params) => getUserActivities(address, params)}
        renderItem={renderItem}
        keyExtractor={(item) => item.txHash}
        scrollEnabled={false}
        queryKeys={["myActivities"]}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  resultCount: {
    color: Colors.white,
    marginVertical: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 20,
    backgroundColor: Colors.PalleteBlack,
  },
})

export default ActivitiesTab
