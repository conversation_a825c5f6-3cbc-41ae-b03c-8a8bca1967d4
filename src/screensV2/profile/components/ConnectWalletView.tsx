import React, { useContext } from "react"
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Image,
} from "react-native"
import { useTranslation } from "react-i18next"

import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { PrimaryButton } from "src/componentsv2/Button"
import iconWallet from "assets/imagesV2/ic_wallet.png"
import { AuthContext } from "../../../context/AuthContext"

const IconPlaceholder: React.FC = () => (
  <Image source={iconWallet} style={styles.walletIcon} />
)

const ConnectWalletView: React.FC = () => {
  const { t } = useTranslation()
  const { connectWallet } = useContext(AuthContext)

  return (
    <View style={styles.screenContainer}>
      <IconPlaceholder />

      <View style={styles.messagesContainer}>
        <Text style={styles.title}>{t("You're not connected yet")}</Text>
        <Text style={styles.subtitle}>
          {t("Please connect your wallet to view your profile details.")}
        </Text>
      </View>
      <PrimaryButton
        title={t("Connect Wallet")}
        onPress={connectWallet}
        height={32}
        textStyle={styles.connectButtonLabel}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 16,
    gap: 16,
  } as ViewStyle,

  walletIcon: {
    width: 40,
    height: 40,
  },

  messagesContainer: {
    flexDirection: "column",
    alignItems: "center",
    padding: 0,
    gap: 6,
  } as ViewStyle,

  title: {
    ...textStyles.XLSemiBold, // 16px, 600, Inter
    color: Colors.white, // #FFFFFF
    textAlign: "center",
    letterSpacing: -0.64, // -0.04em of 16px
  } as TextStyle,

  subtitle: {
    ...textStyles.MMedium, // 12px, 500, Inter
    color: Colors.Neutral500, // #8B8C8F
    textAlign: "center",
    letterSpacing: -0.48, // -0.04em of 12px
  } as TextStyle,

  connectButton: {
    height: 32,
  } as ViewStyle,

  connectButtonLabel: {
    ...textStyles.LMedium, // 14px, 500, Inter
    color: Colors.PalleteBlack, // #17181E
    textAlign: "center",
    letterSpacing: -0.56, // -0.04em of 14px
  } as TextStyle,
})

export default ConnectWalletView
