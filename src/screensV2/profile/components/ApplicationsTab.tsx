import React from "react"
import { StyleSheet, View, ActivityIndicator } from "react-native"
import Colors from "src/config/colors"
import { useProfileContext } from "../context/ProfileContext"
import ListItemCard from "src/screensV2/shared/components/ListItemCard"
import { applicationToListItem } from "src/screensV2/shared/adapters/itemAdapters"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Application, getApplications } from "src/api"
import SimplePagingList from "src/componentsv2/simplepaginglist/SimplePagingList"

const ApplicationsTab: React.FC = () => {
  const { isLoading, address } = useProfileContext()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.Primary500} />
      </View>
    )
  }

  // Navigate to detail screen
  const handleNavigate = (route: string, params: any) => {
    navigation.navigate(route, params)
  }

  // Render từng item
  const renderItem = (item: Application) => {
    const listItemProps = applicationToListItem(item, handleNavigate)
    return <ListItemCard {...listItemProps} />
  }

  return (
    <View style={styles.container}>
      <SimplePagingList<Application>
        getData={(params) => getApplications({ ...params, requester: address })}
        renderItem={renderItem}
        keyExtractor={(item) => item.id.toString()}
        scrollEnabled={false}
        queryKeys={["myApplications"]}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    color: Colors.white,
    textAlign: "center",
  },
  headerContainer: {
    padding: 16,
  },
  headerTitle: {
    color: Colors.white,
    marginBottom: 8,
  },
  headerSubtitle: {
    color: Colors.gray400,
    marginBottom: 16,
  },
  listContent: {
    paddingBottom: 20,
  },
  gridItem: {
    flex: 1,
  },
  columnWrapper: {
    justifyContent: "space-between",
  },
})

export default ApplicationsTab
