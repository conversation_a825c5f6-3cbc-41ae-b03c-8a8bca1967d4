import React, { useContext } from "react"
import {
  StyleSheet,
  View,
  Text,
  Pressable,
  ImageBackground,
  Linking,
} from "react-native"
import { useTranslation } from "react-i18next"
import { AvatarView } from "src/componentsv2"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useProfileContext } from "../context/ProfileContext"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import UserInfoDetails from "./UserInfoDetails"
import imgProfileBackground from "assets/imagesV2/img_profile_background.png"
import TokenBalanceRow from "./TokenBalanceRow"
import ProfileStatus from "./ProfileStatus"
import { PrimaryButton } from "src/componentsv2/Button"
import { AuthContext } from "src/context/AuthContext"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { EDIT_PROFILE } from "src/navigatorV2/routes"
import { BASE_WEB_URL } from "src/config/env"
interface ProfileBackgroundProps {
  onEdit?: () => void
}

const ProfileBackground: React.FC<ProfileBackgroundProps> = ({ onEdit }) => {
  const { t } = useTranslation()
  const { isGuest, profile } = useProfileContext()
  const onShare = async () => {
    const userAddress = profile?.address
    const link = `${BASE_WEB_URL}/profile/${userAddress}/estates`
    Linking.openURL(link)
  }
  return (
    <ImageBackground
      source={imgProfileBackground}
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      {!isGuest && (
        <Pressable style={styles.editButton} onPress={onEdit}>
          <MaterialCommunityIcons
            name="pencil"
            size={12}
            color={Colors.white}
            style={styles.buttonIcon}
          />
          <Text style={styles.editButtonText}>{t("Edit Profile")}</Text>
        </Pressable>
      )}

      <View style={styles.shareButtonContainer}>
        <Pressable style={styles.shareButton} onPress={onShare}>
          <MaterialCommunityIcons
            name="share-variant"
            size={12}
            color={Colors.white}
            style={styles.buttonIcon}
          />
          <Text style={styles.shareButtonText}>{t("Share")}</Text>
        </Pressable>
      </View>
    </ImageBackground>
  )
}

interface ProfileHeaderProps {
  onEdit?: () => void
}

const ProfileHeader: React.FC<ProfileHeaderProps> = () => {
  const { t } = useTranslation()
  const { profile, isGuest } = useProfileContext()
  const { logout } = useContext(AuthContext)
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  if (!profile) {
    return <View style={styles.container} />
  }

  const handleEditProfile = () => {
    navigation.navigate(EDIT_PROFILE)
  }

  return (
    <View style={styles.container}>
      <ProfileBackground onEdit={handleEditProfile} />
      <AvatarView
        size={64}
        avatarUrl={profile.avatarUrl}
        style={styles.avatar}
      />
      <ProfileStatus status={profile.status} />
      <Text style={styles.nameText}>{profile.alias || "-"}</Text>
      <UserInfoDetails />
      {!isGuest && (
        <PrimaryButton
          title={t("Disconnect")}
          textStyle={[textStyles.LMedium]}
          onPress={() => {
            logout(true)
          }}
          width={"100%"}
          height={32}
          style={{ marginBottom: 16 }}
          contentColor={Colors.PalleteWhite}
          color={Colors.Danger500}
        />
      )}
      <TokenBalanceRow />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.PalleteBlack,
    borderRadius: 8,
    padding: 16,
  },
  backgroundImage: {
    height: 100,
    width: "100%",
    borderRadius: 6,
    backgroundColor: Colors.Neutral950,
    justifyContent: "space-between",
  },
  editButton: {
    flexDirection: "row",
    alignSelf: "flex-end",
    alignItems: "center",
    backgroundColor: Colors.Neutral900,
    height: 24,
    paddingHorizontal: 8,
    borderRadius: 4,
    margin: 4,
  },
  buttonIcon: {
    marginRight: 8,
  },
  editButtonText: {
    ...textStyles.SMedium,
    color: Colors.white,
  },
  shareButtonContainer: {
    flexDirection: "row",
    alignSelf: "flex-end",
    margin: 4,
  },
  shareButton: {
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: 8,
  },
  shareButtonText: {
    ...textStyles.SMedium,
    color: Colors.white,
  },
  avatar: {
    marginTop: -32,
    marginStart: 12,
  },
  nameText: {
    ...textStyles.size3XLMedium,
    color: Colors.white,
    marginBottom: 6,
  },
})

export default ProfileHeader
