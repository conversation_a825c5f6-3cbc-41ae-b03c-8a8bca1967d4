import React from "react"
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ImageSourcePropType,
} from "react-native"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import * as Clipboard from "expo-clipboard"
import { showSuccess } from "utils/toast"
import { useTranslation } from "react-i18next"
import iconGlobe from "assets/imagesV2/ic_globe.png"
import iconMail from "assets/imagesV2/ic_mail.png"
import iconPhone from "assets/imagesV2/ic_phone.png"
import iconMapPin from "assets/imagesV2/ic_map_pin.png"
import iconCalendarDays from "assets/imagesV2/ic_calendar_days.png"
import { useProfileContext } from "../context/ProfileContext"
import { useAccount } from "wagmi"
import { convertDateFromTimeStamp } from "utils/timeExt"
import { shortenAddress } from "utils"

interface InfoRowProps {
  icon?: ImageSourcePropType
  label: string
  value: string
}

const InfoRow: React.FC<InfoRowProps> = ({ icon, label, value }) => (
  <View style={styles.infoRow}>
    <View style={styles.labelGroup}>
      {icon && (
        <Image
          source={icon}
          style={viewStyles.size12Icon}
          tintColor={Colors.Neutral500}
        />
      )}
      <Text style={[textStyles.MMedium, styles.labelText]}>{label}</Text>
    </View>
    <Text style={[textStyles.MMedium, styles.valueText]}>{value || "-"}</Text>
  </View>
)

interface WalletAddressRowProps {
  address?: string
  onCopy: () => void
}

const WalletAddressRow: React.FC<WalletAddressRowProps> = ({
  address,
  onCopy,
}) => (
  <View style={styles.detailRow}>
    <Text style={[textStyles.MMedium, styles.labelText]}>Wallet Address</Text>
    <View style={styles.valueGroup}>
      <Text
        style={[textStyles.MMedium, styles.valueText]}
        numberOfLines={1}
        ellipsizeMode="middle"
      >
        {address ? shortenAddress(address) : "-"}
      </Text>
      {address && (
        <TouchableOpacity
          onPress={onCopy}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <MaterialCommunityIcons
            name="content-copy"
            size={12}
            color={Colors.PalleteWhite}
            style={viewStyles.size12Icon}
          />
        </TouchableOpacity>
      )}
    </View>
  </View>
)

const UserInfoDetails: React.FC = () => {
  const { t } = useTranslation()
  const { profile, isGuest } = useProfileContext()
  const { address: walletAddress } = useAccount()

  // Handle copying wallet address
  const copyToClipboard = async () => {
    if (walletAddress) {
      await Clipboard.setStringAsync(walletAddress)
      showSuccess(t("Address copied"))
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.topInfoSection}>
        {isGuest ? (
          <InfoRow
            icon={iconGlobe}
            label={t("Nationality")}
            value={profile?.nationality || ""}
          />
        ) : (
          <>
            <InfoRow
              icon={iconCalendarDays}
              label={t("Date of Birth")}
              value={profile?.dob ? convertDateFromTimeStamp(profile.dob) : ""}
            />
            <InfoRow
              icon={iconGlobe}
              label={t("Nationality")}
              value={profile?.nationality || ""}
            />
            <InfoRow icon={iconMapPin} label={t("Address")} value="" />
            <InfoRow
              icon={iconMail}
              label={t("Email")}
              value={profile?.email || ""}
            />
            <InfoRow
              icon={iconPhone}
              label={t("Phone")}
              value={profile?.phone || ""}
            />
          </>
        )}
      </View>

      {/* Bottom box: Wallet Address & Joined date */}
      <View style={styles.bottomBox}>
        <WalletAddressRow address={walletAddress} onCopy={copyToClipboard} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 0,
    gap: 16,
    width: "100%",
    marginVertical: 16,
  },
  topInfoSection: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 0,
    gap: 8,
    width: "100%",
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 0,
    gap: 6,
    alignSelf: "flex-start",
  },
  labelGroup: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 4,
  },
  labelText: {
    color: Colors.Neutral500,
  },
  valueText: {
    color: Colors.PalleteWhite,
  },
  bottomBox: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 12,
    gap: 12,
    width: "100%",
    backgroundColor: Colors.PalleteBlack,
    borderWidth: 1,
    borderColor: Colors.Neutral950,
    borderRadius: 6,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 0,
    width: "100%",
  },
  valueGroup: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 6,
  },
  separator: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: Colors.Neutral950,
    alignSelf: "stretch",
  },
})

export default UserInfoDetails
