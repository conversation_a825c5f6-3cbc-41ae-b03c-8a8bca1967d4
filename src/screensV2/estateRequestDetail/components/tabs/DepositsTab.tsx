import React from "react"
import { getDepositListByRequestId, TokenizationDeposit } from "src/api"
import QueryKeys from "src/config/queryKeys"
import { useEstateRequestDetailContext } from "src/screensV2/estateRequestDetail/context"
import DepositItem from "./DepositItem"
import SimplePagingList from "../../../../componentsv2/simplepaginglist/SimplePagingList"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"
const DepositsTab: React.FC = () => {
  const { estateRequestDetail } = useEstateRequestDetailContext()
  const estateRequestId = estateRequestDetail?.id || ""
  const { tokenSymbol } = useCurrencies(estateRequestDetail?.currency || "")

  const renderItem = (item: TokenizationDeposit) => (
    <DepositItem
      deposit={item}
      decimals={estateRequestDetail?.decimals || 0}
      unitPrice={estateRequestDetail?.unitPrice}
      currency={tokenSymbol || ""}
    />
  )

  return (
    <SimplePagingList<TokenizationDeposit>
      getData={(params) => getDepositListByRequestId(estateRequestId, params)}
      renderItem={renderItem}
      keyExtractor={(item) => item.id.toString()}
      scrollEnabled={false}
      queryKeys={QueryKeys.ESTATE.DEPOSITS(estateRequestId)}
    />
  )
}

export default DepositsTab
