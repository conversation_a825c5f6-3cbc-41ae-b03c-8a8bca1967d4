import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { Divider } from "react-native-paper"

import { TokenizationDeposit } from "src/api/types"
import { AddressView } from "src/componentsv2"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import {
  formatNumericByDecimals,
  shortenAddress,
  formatCurrency,
} from "src/utils"
import BoxInfoView from "src/screensV2/shared/components/BoxInfoView"

interface DepositItemProps {
  deposit: TokenizationDeposit
  decimals: number
  unitPrice?: string
  currency: string
}

const DepositItem: React.FC<DepositItemProps> = ({
  deposit,
  decimals,
  unitPrice,
  currency,
}) => {
  const { t } = useTranslation()
  const formattedUnitPrice =
    unitPrice && formatNumericByDecimals(unitPrice, decimals)
  const formattedAmount = formatCurrency(deposit.amount)
  const formattedValue = formatCurrency(
    `${Number(formattedUnitPrice) * Number(deposit.amount)}`
  )
  return (
    <View style={styles.itemContainer}>
      <View style={styles.infoRow}>
        <Text style={styles.label}>{t("From")}</Text>
        <AddressView
          address={deposit.depositor.address}
          copy={true}
          style={{ marginStart: 4 }}
        />
      </View>
      <View style={[styles.infoRow, { marginTop: 6 }]}>
        <BoxInfoView>
          <Text style={styles.label}>{t("Amount")}</Text>
          <Text style={styles.value}>{formattedAmount} NFT</Text>
        </BoxInfoView>
        <BoxInfoView style={{ marginLeft: 8 }}>
          <Text style={styles.label}>{t("Value")}</Text>
          <Text style={styles.value}>
            {formattedValue} {currency}
          </Text>
        </BoxInfoView>
      </View>

      <View style={styles.hashContainer}>
        <Text style={styles.label}>{t("Transaction Hash")}</Text>
        <Text style={[styles.value, { color: Colors.Secondary300 }]}>
          {shortenAddress(deposit.transaction.hash)}
        </Text>
      </View>
      <Divider
        style={{ marginTop: 12, backgroundColor: Colors.Neutral950, height: 1 }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    paddingTop: 12,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  infoContainer: {
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: "row",
  },
  infoColumn: {
    marginRight: 8,
    flexDirection: "row",
  },
  infoInput: {
    height: 40,
    borderWidth: 1,
    borderColor: Colors.opacityWhite15,
    borderRadius: 4,
    paddingHorizontal: 12,
    color: Colors.white,
    backgroundColor: "transparent",
  },
  addressContainer: {
    height: 40,
    borderWidth: 1,
    borderColor: Colors.opacityWhite15,
    borderRadius: 4,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  hashContainer: {
    marginTop: 12,
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    ...textStyles.SMedium,
    color: Colors.Neutral300,
  },
  value: {
    ...textStyles.SMedium,
    color: Colors.white,
    marginStart: 4,
  },
})

export default DepositItem
