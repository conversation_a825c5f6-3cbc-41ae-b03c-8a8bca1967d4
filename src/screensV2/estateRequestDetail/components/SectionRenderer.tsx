import React from "react"
import {
  EstateRequestDetailSectionItem,
  EstateRequestDetailSectionType,
} from "../types"
import {
  BasicSection,
  ProgressSection,
  DepositSection,
  DescriptionSection,
  LegalSection,
  OnChainSection,
  TraitsSection,
  DepositsDepositorsSection,
} from "./sections"

interface SectionRendererProps {
  item: EstateRequestDetailSectionItem
}

const SectionRenderer: React.FC<SectionRendererProps> = ({ item }) => {
  switch (item.type) {
    case EstateRequestDetailSectionType.BASIC:
      return <BasicSection item={item} />
    case EstateRequestDetailSectionType.PROGRESS:
      return <ProgressSection item={item} />
    case EstateRequestDetailSectionType.DEPOSIT:
      return <DepositSection item={item} />
    case EstateRequestDetailSectionType.DESCRIPTION:
      return <DescriptionSection item={item} />
    case EstateRequestDetailSectionType.LEGAL:
      return <LegalSection item={item} />
    case EstateRequestDetailSectionType.ONCHAIN:
      return <OnChainSection item={item} />
    case EstateRequestDetailSectionType.TRAITS:
      return <TraitsSection item={item} />
    case EstateRequestDetailSectionType.DEPOSITS_DEPOSITORS:
      return <DepositsDepositorsSection />
    default:
      return null
  }
}

export default SectionRenderer
