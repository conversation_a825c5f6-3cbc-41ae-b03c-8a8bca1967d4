import React from "react"
import { ProgressSectionItem } from "../../types"
import { ProgressSectionView } from "src/screensV2/shared/components"

interface ProgressSectionProps {
  item: ProgressSectionItem
}

const ProgressSection: React.FC<ProgressSectionProps> = ({ item }) => {
  const tokenizationRequest = item.estateRequest

  const {
    totalSupply = "0",
    minSellingAmount = "0",
    maxSellingAmount = "0",
    soldAmount = "0",
    publicSaleEndsAtInSeconds = 0,
    state,
  } = tokenizationRequest

  return (
    <ProgressSectionView
      totalSupply={totalSupply}
      minSellingAmount={minSellingAmount}
      maxSellingAmount={maxSellingAmount}
      soldAmount={soldAmount}
      publicSaleEndsAtInSeconds={publicSaleEndsAtInSeconds}
      state={state}
    />
  )
}

export default ProgressSection
