import React from "react"
import { BasicSectionItem } from "../../types"
import { BasicSectionEstateView } from "src/screensV2/shared/components"
import { EstateZone, EstateTokenAreaUnit } from "src/api/types/estate"

interface BasicSectionProps {
  item: BasicSectionItem
}

const BasicSection: React.FC<BasicSectionProps> = ({ item }) => {
  const tokenizationRequest = item.estateRequest

  const {
    metadata: {
      estatePhotoUrls: images = [],
      metadata: {
        name = "",
        address = "",
        area = { area: 0, unit: EstateTokenAreaUnit.SQM },
        created_at: createdAt = 0,
        locale_detail = {
          zone: EstateZone.VIETNAM,
          vietnam: {
            level1: "",
            level2: "",
            level3: "",
          },
        },
      } = {},
    } = {},
    requester: {
      avatarUrl: sellerAvatar = "",
      address: sellerAddress = "",
    } = {},
    totalSupply = "0",
  } = tokenizationRequest

  return (
    <BasicSectionEstateView
      images={images}
      name={name}
      sellerAvatar={sellerAvatar}
      sellerAddress={sellerAddress}
      createdAt={createdAt}
      address={address}
      totalSupply={totalSupply}
      area={area}
      locale_detail={locale_detail}
    />
  )
}

export default BasicSection
