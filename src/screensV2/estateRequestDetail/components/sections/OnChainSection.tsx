import React from "react"
import { StyleSheet, View } from "react-native"
import { OnChainSectionItem } from "../../types"
import { OnChainView } from "../../../shared/components"

interface OnChainSectionProps {
  item: OnChainSectionItem
}

const OnChainSection: React.FC<OnChainSectionProps> = ({ item }) => {
  const { requestId, uri } = item

  return (
    <View style={styles.container}>
      <OnChainView requestId={requestId} uri={uri} />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
})

export default OnChainSection
