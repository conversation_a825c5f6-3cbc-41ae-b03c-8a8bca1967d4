import React, { createContext, useContext, ReactNode } from "react"
import { EstateRequestDetailContextState } from "./types"
import {
  Currency,
  TokenizationDepositor,
  TokenizationRequest,
} from "src/api/types"
import { useEstateRequestDetailProvider } from "../hooks/useEstateRequestDetailProvider"

// Create the context with default values
const EstateRequestDetailContext =
  createContext<EstateRequestDetailContextState>({
    estateRequestId: "",
    estateRequestDetail: null,

    currencies: [],
    isLoadingEstateRequestDetail: false,
    estateRequestDetailError: null,
    refreshEstateRequestDetail: async () => {},
  })

// Hook to use the context
export const useEstateRequestDetailContext = () =>
  useContext(EstateRequestDetailContext)

// Provider props
interface EstateRequestDetailProviderProps {
  children: ReactNode
  estateRequestId: string

  // Initial data (optional, for testing or SSR)
  initialEstateRequestDetail?: TokenizationRequest | null
  initialDepositors?: TokenizationDepositor[]
  initialCurrencies?: Currency[]
}

// Provider component
export const EstateRequestDetailProvider: React.FC<
  EstateRequestDetailProviderProps
> = ({
  children,
  estateRequestId,
  initialEstateRequestDetail,
  initialCurrencies,
}) => {
  // Use the custom hook to get the context value
  const contextValue = useEstateRequestDetailProvider(
    estateRequestId,
    initialEstateRequestDetail,
    initialCurrencies
  )

  return (
    <EstateRequestDetailContext.Provider value={contextValue}>
      {children}
    </EstateRequestDetailContext.Provider>
  )
}

export default EstateRequestDetailContext
