import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import { getEstateRequestDetailById, TokenizationRequest } from "src/api"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "useEstateRequestDetail" })

/**
 * Hook to fetch estate request details by ID
 * @param estateRequestId The ID of the estate request to fetch
 */
export const useEstateRequestDetail = (estateRequestId: string) => {
  logger.debug("Fetching estate request detail", { estateRequestId })

  return useQueryWithErrorHandling<TokenizationRequest>({
    queryKey: QueryKeys.ESTATE.REQUEST_DETAIL(estateRequestId),
    queryFn: () => getEstateRequestDetailById(estateRequestId),
    refetchInterval: 10_000,
    refetchOnMount: true,
    refetchIntervalInBackground: true,
  })
}
