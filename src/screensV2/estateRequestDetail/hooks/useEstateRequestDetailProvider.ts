import { useEstateRequestDetail } from "./useEstateRequestDetail"
import { useCurrencies } from "./useCurrencies"
import { EstateRequestDetailContextState } from "../context/types"
import { Currency, TokenizationRequest } from "src/api/types"

/**
 * Hook that combines all data fetching hooks for the EstateRequestDetail screen
 * and provides the context value
 * @param estateRequestId The ID of the estate request to fetch data for
 */
export const useEstateRequestDetailProvider = (
  estateRequestId: string,
  initialEstateRequestDetail?: TokenizationRequest | null,
  initialCurrencies?: Currency[]
): EstateRequestDetailContextState => {
  const {
    data: estateRequestDetail = initialEstateRequestDetail || null,
    isLoading: isLoadingEstateRequestDetail,
    error: estateRequestDetailError,
    refetch: refetchEstateRequestDetail,
  } = useEstateRequestDetail(estateRequestId)

  // Fetch currencies
  const { data: currencies = initialCurrencies || [] } = useCurrencies()

  return {
    // Core data
    estateRequestId,
    estateRequestDetail,
    currencies,
    isLoadingEstateRequestDetail,
    estateRequestDetailError: estateRequestDetailError as Error | null,

    refreshEstateRequestDetail: async () => {
      await refetchEstateRequestDetail()
    },
  }
}
