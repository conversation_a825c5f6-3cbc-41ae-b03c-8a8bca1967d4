import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import { getDepositorsByEstateRequestId, TokenizationDepositor } from "src/api"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "useEstateRequestDepositors" })

/**
 * Hook to fetch depositors for an estate request
 * @param estateRequestId The ID of the estate request to fetch depositors for
 */
export const useEstateRequestDepositors = (estateRequestId: string) => {
  logger.debug("Fetching estate request depositors", { estateRequestId })

  return useQueryWithErrorHandling<TokenizationDepositor[]>({
    queryKey: QueryKeys.ESTATE.REQUEST_DEPOSITORS(estateRequestId),
    queryFn: () => getDepositorsByEstateRequestId(estateRequestId),
    refetchInterval: 10_000,
    refetchOnMount: true,
    refetchIntervalInBackground: true,
  })
}
