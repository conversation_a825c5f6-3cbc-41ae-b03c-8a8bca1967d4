import React, { useCallback } from "react"
import { StyleSheet, View, FlatList, ListRenderItem } from "react-native"
import { Background, EmptyView, SimpleLoadingView } from "src/componentsv2"
import { useTranslation } from "react-i18next"
import { useEstateDetailContext } from "./context"
import { useEstateSections } from "./hooks"
import { EstateDetailSectionItem } from "./types"
import { SectionRenderer } from "./components"
import { EstateDetailHeader } from "../shared/components"

// The main content component that uses the context
const EstateDetailContent: React.FC = () => {
  const { t } = useTranslation()
  const { estateDetailError } = useEstateDetailContext()
  const { sections, isLoading } = useEstateSections()

  // Define callbacks at the top level
  const renderItem: ListRenderItem<EstateDetailSectionItem> = useCallback(
    ({ item }) => {
      return <SectionRenderer item={item} />
    },
    []
  )

  const keyExtractor = useCallback(
    (item: EstateDetailSectionItem) => item.id,
    []
  )

  // Render based on state
  if (isLoading) {
    return <SimpleLoadingView visible={isLoading} />
  }

  if (estateDetailError) {
    return <EmptyView subtitle={t("Failed to load estate details")} />
  }

  if (!sections || sections.length === 0) {
    return <EmptyView subtitle={t("No estate details found")} />
  }

  // Show estate details with FlatList
  return (
    <FlatList
      data={sections}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainer}
    />
  )
}

// The main screen component that provides the context
const EstateDetailView: React.FC = () => {
  const { estateDetail } = useEstateDetailContext()

  return (
    <Background>
      <View style={styles.container}>
        <EstateDetailHeader status={estateDetail?.metadata?.metadata?.status} />
        <EstateDetailContent />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 20,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 50,
  },
})

export default EstateDetailView
