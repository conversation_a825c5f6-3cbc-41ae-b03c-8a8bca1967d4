import React from "react"
import { RouteProp } from "@react-navigation/native"
import { useIsFocused } from "@react-navigation/native"
import { RootStackParamList } from "src/navigatorV2"
import { EstateDetailProvider } from "./context"
import Logger from "src/utils/logger"
import EstateDetailView from "./EstateDetailView"

const logger = new Logger({ tag: "EstateDetailScreen" })

// Props for the screen
interface EstateDetailScreenProps {
  route: RouteProp<RootStackParamList, "EstateDetail">
}

// The main screen component that provides the context
const EstateDetailScreen: React.FC<EstateDetailScreenProps> = ({ route }) => {
  const { estateId } = route.params
  const isFocused = useIsFocused()

  logger.debug("Rendering EstateDetailScreen", { estateId, isFocused })

  return (
    <EstateDetailProvider estateId={estateId} isFocused={isFocused}>
      <EstateDetailView />
    </EstateDetailProvider>
  )
}

export default EstateDetailScreen
