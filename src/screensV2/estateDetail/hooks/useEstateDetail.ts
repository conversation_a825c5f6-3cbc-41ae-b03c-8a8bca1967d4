import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import { getEstateDetailById, Estate } from "src/api"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "useEstateDetail" })

/**
 * Hook to fetch estate details by ID
 * @param estateId The ID of the estate to fetch
 * @param isFocused Whether the screen is currently focused
 */
export const useEstateDetail = (
  estateId: string,
  isFocused: boolean = true
) => {
  logger.debug("Fetching estate detail", { estateId, isFocused })

  return useQueryWithErrorHandling<Estate>({
    queryKey: QueryKeys.ESTATE.DETAIL(estateId),
    queryFn: () => getEstateDetailById(estateId),
    refetchInterval: isFocused ? 10_000 : false,
    refetchIntervalInBackground: false,
    enabled: isFocused,
  })
}
