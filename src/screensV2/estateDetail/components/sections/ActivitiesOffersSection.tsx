import React, { useState } from "react"
import { StyleSheet, View } from "react-native"
import { useTranslation } from "react-i18next"

import { textStyles } from "src/config/styles"
import { TabSelector } from "src/screensV2/estate/components"
import ActivitiesTab from "src/screensV2/estateDetail/components/components/tabs/ActivitiesTab"
import HoldersTab from "src/screensV2/estateDetail/components/components/tabs/HoldersTab"
import OffersTab from "src/screensV2/estateDetail/components/components/tabs/OffersTab"

const ActivitiesOffersSection: React.FC = () => {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState(0)

  const tabTitles = [t("Offers"), t("Activities"), t("Holders")]

  return (
    <View style={styles.contentContainer}>
      <TabSelector
        tabTitles={tabTitles}
        selectedIndex={activeTab}
        setTabIndex={setActiveTab}
      />

      {/* Tab Content */}
      <View style={styles.tabContent}>
        {activeTab === 0 ? (
          <OffersTab />
        ) : activeTab === 1 ? (
          <ActivitiesTab />
        ) : (
          <HoldersTab />
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  contentContainer: {
    paddingVertical: 16,
  },
  sectionTitle: {
    ...textStyles.titleM,
    marginBottom: 16,
    textAlign: "center",
    textTransform: "uppercase",
  },
  tabContent: {
    minHeight: 200,
    marginTop: 16,
  },
})

export default ActivitiesOffersSection
