import React from "react"
import { StyleSheet, View } from "react-native"
import { LegalSectionItem } from "../../types"
import { LegalView } from "src/screensV2/shared/components"

interface LegalSectionProps {
  item: LegalSectionItem
}

const LegalSection: React.FC<LegalSectionProps> = ({ item }) => {
  return (
    <View style={styles.container}>
      <LegalView
        metadataId={item.metadataId}
        tokenMintEventTxHash={item.tokenMintEventTxHash}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
})

export default LegalSection
