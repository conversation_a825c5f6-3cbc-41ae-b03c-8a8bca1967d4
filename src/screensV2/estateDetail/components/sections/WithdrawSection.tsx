import React from "react"
import { WithdrawSectionItem } from "../../types"
import { View, StyleSheet } from "react-native"
import Colors from "src/config/colors"
import {
  EstateValueView,
  WithdrawSectionView,
} from "src/screensV2/shared/components"

interface WithdrawSectionProps {
  item: WithdrawSectionItem
}

const WithdrawSection: React.FC<WithdrawSectionProps> = ({ item }) => {
  const tokenizationRequest = item?.estate?.tokenizationRequest
  if (!tokenizationRequest) {
    return null
  }

  const {
    maxSellingAmount = "0",
    soldAmount = "0",
    unitPrice = "0",
    totalSupply = "0",
    decimals = 0,
    currency = "",
    state,
    id = "",
  } = tokenizationRequest

  return (
    <View style={styles.container}>
      <EstateValueView
        unitPrice={unitPrice}
        totalSupply={totalSupply}
        soldAmount={soldAmount}
        maxSellingAmount={maxSellingAmount}
        decimals={decimals}
        currency={currency}
        state={state}
      />
      <WithdrawSectionView
        requestId={id}
        state={state}
        currency={currency}
        decimals={decimals}
        unitPrice={unitPrice}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.Neutral950,
    borderRadius: 6,
    padding: 12,
    gap: 12,
  },
})

export default WithdrawSection
