import React from "react"
import { EstateTokenBalance, getPaginatedEstateTokenBalances } from "src/api"
import { fixedPointMultiply, formatCurrencyByDecimals } from "utils"
import { logger } from "utils/logger"
import { useEstateDetailContext } from "src/screensV2/estateDetail/context"
import HolderItem from "src/screensV2/shared/components/HolderItem"
import SimplePagingList from "src/componentsv2/simplepaginglist/SimplePagingList"
import QueryKeys from "src/config/queryKeys"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"

logger.createLogger("Holders tab")

const HoldersTab: React.FC = () => {
  const { estateDetail, estateId } = useEstateDetailContext()

  const decimals = estateDetail?.decimals || 0
  const unitPrice = estateDetail?.tokenizationRequest?.unitPrice
  const { tokenSymbol } = useCurrencies(
    estateDetail?.tokenizationRequest?.currency || ""
  )
  logger.info("Render holders tab")

  const renderItem = (item: EstateTokenBalance) => {
    const value = fixedPointMultiply(
      BigInt(unitPrice || "0"),
      BigInt(item.value),
      decimals
    )
    const formattedAmount = formatCurrencyByDecimals(item.value, decimals)
    const formattedValue = formatCurrencyByDecimals(value.toString(), decimals)
    return (
      <HolderItem
        fromAddress={item.account.address}
        amount={formattedAmount}
        value={formattedValue}
        currency={tokenSymbol || ""}
      />
    )
  }

  return (
    <SimplePagingList<EstateTokenBalance>
      getData={(params) => getPaginatedEstateTokenBalances(estateId, params)}
      renderItem={renderItem}
      keyExtractor={(item) => item.account.address}
      scrollEnabled={false}
      queryKeys={QueryKeys.ESTATE.BALANCES(estateId)}
    />
  )
}
export default HoldersTab
