import React from "react"
import { logger } from "utils/logger"
import ActivityItem from "./ActivityItem"
import SimplePagingList from "src/componentsv2/simplepaginglist/SimplePagingList"
import QueryKeys from "src/config/queryKeys"
import { EstateActivity, getPaginatedEstateActivities } from "src/api"
import { useEstateDetailContext } from "../../../context"

logger.createLogger("Activities tab")

const ActivitiesTab: React.FC = () => {
  const { estateId, estateDetail } = useEstateDetailContext()

  logger.info("Render activities tab")

  const renderItem = (item: EstateActivity) => {
    return (
      <ActivityItem activity={item} decimals={estateDetail?.decimals || 0} />
    )
  }

  return (
    <SimplePagingList<EstateActivity>
      getData={(params) => getPaginatedEstateActivities(estateId, params)}
      renderItem={renderItem}
      keyExtractor={(item, index) => item.txHash + index.toString()}
      scrollEnabled={false}
      queryKeys={QueryKeys.ESTATE.ACTIVITIES(estateId)}
    />
  )
}

export default ActivitiesTab
