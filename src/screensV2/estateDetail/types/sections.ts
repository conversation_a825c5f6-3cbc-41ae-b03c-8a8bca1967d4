import { Estate, EstateTokenAttribute } from "src/api/types"

/**
 * Enum for section types in the EstateDetail screen
 */
export enum EstateDetailSectionType {
  BASIC = "basic",
  PROGRESS = "progress",
  TRANSFER_NFT = "transferNFT",
  WITHDRAW = "withdraw",
  DESCRIPTION = "description",
  ONCHAIN = "onchain",
  TRAITS = "traits",
  LEGAL = "legal",
  ACTIVITIES_OFFERS = "activitiesOffers",
}

/**
 * Base interface for all section items
 */
export interface BaseSectionItem {
  type: EstateDetailSectionType
  id: string
}

/**
 * Description section item
 */
export interface DescriptionSectionItem extends BaseSectionItem {
  type: EstateDetailSectionType.DESCRIPTION
  description: string
}

/**
 * Basic section item
 */
export interface BasicSectionItem extends BaseSectionItem {
  type: EstateDetailSectionType.BASIC
  estate: Estate
}

/**
 * Progress section item
 */
export interface ProgressSectionItem extends BaseSectionItem {
  type: EstateDetailSectionType.PROGRESS
  estate: Estate
}

/**
 * TransferNFT section item
 */
export interface TransferNFTSectionItem extends BaseSectionItem {
  type: EstateDetailSectionType.TRANSFER_NFT
  estate: Estate
}

/**
 * Withdraw section item
 */
export interface WithdrawSectionItem extends BaseSectionItem {
  type: EstateDetailSectionType.WITHDRAW
  estate: Estate
}

/**
 * OnChain section item
 */
export interface OnChainSectionItem extends BaseSectionItem {
  type: EstateDetailSectionType.ONCHAIN
  requestId: string
  uri: string
}

/**
 * Traits section item
 */
export interface TraitsSectionItem extends BaseSectionItem {
  type: EstateDetailSectionType.TRAITS
  estateTokenTraits: EstateTokenAttribute[]
}

/**
 * Legal section item
 */
export interface LegalSectionItem extends BaseSectionItem {
  type: EstateDetailSectionType.LEGAL
  metadataId: number
  tokenMintEventTxHash: string
}

/**
 * Activities/Offers section item
 */
export interface ActivitiesOffersSectionItem extends BaseSectionItem {
  type: EstateDetailSectionType.ACTIVITIES_OFFERS
  estate: Estate
}

/**
 * Union type for all section items
 */
export type EstateDetailSectionItem =
  | BasicSectionItem
  | ProgressSectionItem
  | TransferNFTSectionItem
  | WithdrawSectionItem
  | DescriptionSectionItem
  | OnChainSectionItem
  | TraitsSectionItem
  | LegalSectionItem
  | ActivitiesOffersSectionItem
