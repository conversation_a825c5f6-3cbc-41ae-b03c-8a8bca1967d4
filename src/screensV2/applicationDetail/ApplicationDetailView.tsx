import React, { useCallback } from "react"
import { StyleSheet, View, FlatList, ListRenderItem } from "react-native"
import { Background, EmptyView, SimpleLoadingView } from "src/componentsv2"
import { useApplicationDetailContext } from "./context"
import { useApplicationSections } from "./hooks"
import { ApplicationDetailSectionItem } from "./types"
import { SectionRenderer } from "./components"
import { EstateDetailHeader } from "../shared/components"

// Component to render the content of the screen
const ApplicationDetailContent: React.FC = () => {
  const { applicationDetail, isLoadingApplicationDetail, refreshAll } =
    useApplicationDetailContext()
  const sections = useApplicationSections()

  // Render each section
  const renderItem: ListRenderItem<ApplicationDetailSectionItem> = useCallback(
    ({ item }) => <SectionRenderer item={item} />,
    []
  )

  // Key extractor for the FlatList
  const keyExtractor = useCallback(
    (item: ApplicationDetailSectionItem) => item.id,
    []
  )

  // Show loading state
  if (isLoadingApplicationDetail) {
    return <SimpleLoadingView visible={isLoadingApplicationDetail} />
  }

  // Show empty state if no data
  if (!applicationDetail) {
    return <EmptyView />
  }

  // Show application details with FlatList
  return (
    <FlatList
      data={sections}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainer}
      onRefresh={refreshAll}
      refreshing={isLoadingApplicationDetail}
    />
  )
}

// The main screen component that provides the context
const ApplicationDetailView: React.FC = () => {
  const { applicationDetail } = useApplicationDetailContext()

  return (
    <Background>
      <View style={styles.container}>
        <EstateDetailHeader
          status={applicationDetail?.metadata?.metadata?.status}
        />
        <ApplicationDetailContent />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 20,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 50,
  },
})

export default ApplicationDetailView
