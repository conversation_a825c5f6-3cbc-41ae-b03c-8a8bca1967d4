import { useQueryClient } from "@tanstack/react-query"
import { useApplicationDetail } from "./useApplicationDetail"
import { useCurrencies } from "./useCurrencies"
import { ApplicationDetailContextState } from "../context/types"
import QueryKeys from "src/config/queryKeys"
import Logger from "src/utils/logger"
import { Application, Currency } from "src/api/types"

const logger = new Logger({ tag: "useApplicationDetailProvider" })

/**
 * Hook that combines all data fetching hooks for the ApplicationDetail screen
 * and provides the context value
 * @param applicationId The ID of the application to fetch data for
 */
export const useApplicationDetailProvider = (
  applicationId: string,
  initialApplicationDetail?: Application | null,
  initialCurrencies?: Currency[]
): ApplicationDetailContextState => {
  const queryClient = useQueryClient()

  // Fetch application detail
  const {
    data: applicationDetail = initialApplicationDetail || null,
    isLoading: isLoadingApplicationDetail,
    error: applicationDetailError,
    refetch: refetchApplicationDetail,
  } = useApplicationDetail(applicationId)

  // Fetch currencies
  const {
    data: currencies = initialCurrencies || [],
    isLoading: isLoadingCurrencies,
    error: currenciesError,
    refetch: refetchCurrencies,
  } = useCurrencies()

  // Refresh all data
  const refreshAll = async () => {
    logger.debug("Refreshing all application detail data")
    await Promise.all([
      queryClient.invalidateQueries({
        queryKey: QueryKeys.PROFILE.APPLICATION_DETAIL(applicationId),
      }),
      queryClient.invalidateQueries({
        queryKey: QueryKeys.CURRENCY.LIST,
      }),
    ])
  }

  return {
    // Core data
    applicationId,
    applicationDetail,

    // Related data
    currencies,

    // Loading states
    isLoadingApplicationDetail,
    isLoadingCurrencies,

    // Error states
    applicationDetailError: applicationDetailError as Error | null,
    currenciesError: currenciesError as Error | null,

    // Actions
    refreshAll,
    refreshApplicationDetail: async () => {
      await refetchApplicationDetail()
    },
    refreshCurrencies: async () => {
      await refetchCurrencies()
    },
  }
}
