import { useMemo } from "react"
import { useApplicationDetailContext } from "../context"
import {
  ApplicationDetailSectionItem,
  ApplicationDetailSectionType,
} from "../types"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "useApplicationSections" })

/**
 * Hook to create sections for the ApplicationDetail screen
 */
export const useApplicationSections = () => {
  const { applicationId, applicationDetail } = useApplicationDetailContext()

  // Create sections based on the data
  const sections = useMemo(() => {
    if (!applicationDetail || !applicationDetail.metadata) return []

    logger.debug("Creating sections for application", { applicationId })

    const result: ApplicationDetailSectionItem[] = []

    // Basic section
    result.push({
      type: ApplicationDetailSectionType.BASIC,
      id: `${ApplicationDetailSectionType.BASIC}-${applicationId}`,
      application: applicationDetail,
    })

    // Progress section
    result.push({
      type: ApplicationDetailSectionType.PROGRESS,
      id: `${ApplicationDetailSectionType.PROGRESS}-${applicationId}`,
      application: applicationDetail,
    })

    // Deposit section
    result.push({
      type: ApplicationDetailSectionType.DEPOSIT,
      id: `${ApplicationDetailSectionType.DEPOSIT}-${applicationId}`,
      application: applicationDetail,
    })

    // Description section
    result.push({
      type: ApplicationDetailSectionType.DESCRIPTION,
      id: `${ApplicationDetailSectionType.DESCRIPTION}-${applicationId}`,
      description: applicationDetail.metadata?.metadata?.description,
    })

    // Traits section
    result.push({
      type: ApplicationDetailSectionType.TRAITS,
      id: `${ApplicationDetailSectionType.TRAITS}-${applicationId}`,
      estateTokenTraits: applicationDetail.metadata?.metadata?.attributes,
    })

    // Legal section
    result.push({
      type: ApplicationDetailSectionType.LEGAL,
      id: `${ApplicationDetailSectionType.LEGAL}-${applicationId}`,
      application: applicationDetail,
    })

    return result
  }, [applicationId, applicationDetail])

  return sections
}
