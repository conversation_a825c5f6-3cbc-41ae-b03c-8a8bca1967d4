import React from "react"
import { DepositSectionItem } from "../../types"
import { View, StyleSheet } from "react-native"
import {
  EstateValueView,
  DepositActionView,
} from "src/screensV2/shared/components"
import Colors from "src/config/colors"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"
import { formatNumericByDecimals } from "src/utils/format"

interface DepositSectionProps {
  item: DepositSectionItem
}

const DepositSection: React.FC<DepositSectionProps> = ({ item }) => {
  const application = item.application

  const {
    maxSellingAmount = "0",
    unitPrice = "0",
    totalSupply = "0",
    decimals = 0,
    currency = "",
    state,
    tokenizationRequestId = "",
  } = application

  const { tokenSymbol } = useCurrencies(currency)

  return (
    <View style={styles.container}>
      <EstateValueView
        unitPrice={unitPrice}
        totalSupply={formatNumericByDecimals(totalSupply, decimals)}
        maxSellingAmount={formatNumericByDecimals(maxSellingAmount, decimals)}
        decimals={decimals}
        currency={currency}
        state={state}
      />

      <DepositActionView
        state={state}
        tokenSymbol={tokenSymbol ?? ""}
        requestId={tokenizationRequestId}
        currencyId={currency}
        decimals={decimals}
        unitPrice={unitPrice}
        maxSellingAmount={formatNumericByDecimals(maxSellingAmount, decimals)}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.Neutral950,
    borderRadius: 6,
    padding: 12,
    gap: 12,
  },
})

export default DepositSection
