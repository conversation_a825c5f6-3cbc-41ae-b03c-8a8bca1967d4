import React from "react"
import { ProgressSectionItem } from "../../types"
import { ProgressSectionView } from "src/screensV2/shared/components"
import { formatNumericByDecimals } from "src/utils/format"

interface ProgressSectionProps {
  item: ProgressSectionItem
}

const ProgressSection: React.FC<ProgressSectionProps> = ({ item }) => {
  const application = item.application

  const {
    totalSupply = "0",
    minSellingAmount = "0",
    maxSellingAmount = "0",
    state,
    decimals = 0,
  } = application

  return (
    <ProgressSectionView
      isApplicationDetail={true}
      totalSupply={formatNumericByDecimals(totalSupply, decimals)}
      minSellingAmount={formatNumericByDecimals(minSellingAmount, decimals)}
      maxSellingAmount={formatNumericByDecimals(maxSellingAmount, decimals)}
      publicSaleEndsAtInSeconds={0}
      state={state}
    />
  )
}

export default ProgressSection
