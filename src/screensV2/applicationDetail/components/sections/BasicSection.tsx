import React from "react"
import { BasicSectionItem } from "../../types"
import { BasicSectionEstateView } from "src/screensV2/shared/components"
import { formatNumericByDecimals } from "src/utils/format"
import { EstateZone, EstateTokenAreaUnit } from "src/api/types/estate"

interface BasicSectionProps {
  item: BasicSectionItem
}

const BasicSection: React.FC<BasicSectionProps> = ({ item }) => {
  const application = item.application

  const {
    metadata: {
      estatePhotoUrls: images = [],
      metadata: {
        name = "",
        address = "",
        area = { area: 0, unit: EstateTokenAreaUnit.SQM },
        locale_detail = {
          zone: EstateZone.VIETNAM,
          vietnam: {
            level1: "",
            level2: "",
            level3: "",
          },
        },
      } = {},
    } = {},
    requester: {
      avatarUrl: sellerAvatar = "",
      address: sellerAddress = "",
    } = {},
    createdAtInSeconds = 0,
    totalSupply = "0",
    decimals = 0,
  } = application

  return (
    <BasicSectionEstateView
      images={images}
      name={name}
      sellerAvatar={sellerAvatar}
      sellerAddress={sellerAddress}
      createdAt={createdAtInSeconds}
      address={address}
      totalSupply={formatNumericByDecimals(totalSupply, decimals)}
      area={area}
      locale_detail={locale_detail}
    />
  )
}

export default BasicSection
