import React from "react"
import {
  ApplicationDetailSectionItem,
  ApplicationDetailSectionType,
} from "../types"
import {
  BasicSection,
  ProgressSection,
  DepositSection,
  DescriptionSection,
  TraitsSection,
} from "./sections"

interface SectionRendererProps {
  item: ApplicationDetailSectionItem
}

const SectionRenderer: React.FC<SectionRendererProps> = ({ item }) => {
  switch (item.type) {
    case ApplicationDetailSectionType.BASIC:
      return <BasicSection item={item} />
    case ApplicationDetailSectionType.PROGRESS:
      return <ProgressSection item={item} />
    case ApplicationDetailSectionType.DEPOSIT:
      return <DepositSection item={item} />
    case ApplicationDetailSectionType.DESCRIPTION:
      return <DescriptionSection item={item} />
    case ApplicationDetailSectionType.LEGAL:
      return null
    case ApplicationDetailSectionType.TRAITS:
      return <TraitsSection item={item} />
    default:
      return null
  }
}

export default SectionRenderer
