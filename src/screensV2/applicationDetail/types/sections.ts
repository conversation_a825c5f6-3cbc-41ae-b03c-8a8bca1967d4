import { Application, EstateTokenAttribute } from "src/api/types"

/**
 * Enum for section types in the ApplicationDetail screen
 */
export enum ApplicationDetailSectionType {
  BASIC = "basic",
  PROGRESS = "progress",
  DEPOSIT = "deposit",
  DESCRIPTION = "description",
  LEGAL = "legal",
  TRAITS = "traits",
}

/**
 * Base interface for all section items
 */
export interface BaseSectionItem {
  type: ApplicationDetailSectionType
  id: string
}

/**
 * Description section item
 */
export interface DescriptionSectionItem extends BaseSectionItem {
  type: ApplicationDetailSectionType.DESCRIPTION
  description: string
}

/**
 * Basic section item
 */
export interface BasicSectionItem extends BaseSectionItem {
  type: ApplicationDetailSectionType.BASIC
  application: Application
}

/**
 * Progress section item
 */
export interface ProgressSectionItem extends BaseSectionItem {
  type: ApplicationDetailSectionType.PROGRESS
  application: Application
}
/**
 * Deposit section item
 */
export interface DepositSectionItem extends BaseSectionItem {
  type: ApplicationDetailSectionType.DEPOSIT
  application: Application
}
/**
 * Legal section item
 */
export interface TraitsSectionItem extends BaseSectionItem {
  type: ApplicationDetailSectionType.TRAITS
  estateTokenTraits: EstateTokenAttribute[]
}

/**
 * Legal section item
 */
export interface LegalSectionItem extends BaseSectionItem {
  type: ApplicationDetailSectionType.LEGAL
  application: Application
}

/**
 * Union type for all section items
 */
export type ApplicationDetailSectionItem =
  | BasicSectionItem
  | ProgressSectionItem
  | DepositSectionItem
  | DescriptionSectionItem
  | LegalSectionItem
  | TraitsSectionItem
