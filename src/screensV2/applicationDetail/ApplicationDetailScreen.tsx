import React from "react"
import { RouteProp } from "@react-navigation/native"
import { RootStackParamList } from "src/navigatorV2"
import { ApplicationDetailProvider } from "./context"
import Logger from "src/utils/logger"
import ApplicationDetailView from "./ApplicationDetailView"

const logger = new Logger({ tag: "ApplicationDetailScreen" })

// Props for the screen
interface ApplicationDetailScreenProps {
  route: RouteProp<RootStackParamList, "ApplicationDetail">
}

// The main screen component
const ApplicationDetailScreen: React.FC<ApplicationDetailScreenProps> = ({
  route,
}) => {
  const { applicationId } = route.params

  logger.debug("Rendering ApplicationDetailScreen", { applicationId })

  return (
    <ApplicationDetailProvider applicationId={applicationId}>
      <ApplicationDetailView />
    </ApplicationDetailProvider>
  )
}

export default ApplicationDetailScreen
