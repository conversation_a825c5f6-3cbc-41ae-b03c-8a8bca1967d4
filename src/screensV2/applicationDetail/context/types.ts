import { Application, Currency } from "src/api/types"

/**
 * Types for the ApplicationDetail context
 */
export interface ApplicationDetailContextState {
  // Core data
  applicationId: string
  applicationDetail: Application | null

  // Related data
  currencies: Currency[]

  // Loading states
  isLoadingApplicationDetail: boolean
  isLoadingCurrencies: boolean

  // Error states
  applicationDetailError: Error | null
  currenciesError: Error | null

  // Actions
  refreshAll: () => Promise<void>
  refreshApplicationDetail: () => Promise<void>
  refreshCurrencies: () => Promise<void>
}
