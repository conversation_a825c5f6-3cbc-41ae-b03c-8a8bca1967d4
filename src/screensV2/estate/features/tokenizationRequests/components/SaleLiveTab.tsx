import React, { useCallback } from "react"
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  View,
} from "react-native"
import { TokenizationRequest } from "src/api"
import Colors from "src/config/colors"
import { EmptyView } from "src/componentsv2"
import { ResultsHeader } from "src/screensV2/estate/components"
import { GridSaleLiveRenderItem } from "src/screensV2/shared/components"
import { useTokenizationRequestsByState } from "../hooks/useTokenizationRequestsByState"
import { TokenizationRequestState } from "src/api/types/estate"
import { calculateGridItemDimensions } from "src/utils/layout"

const { itemWidth, itemSpacing } = calculateGridItemDimensions({
  numColumns: 2,
})

const SaleLiveTab: React.FC = () => {
  const {
    tokenizationRequests,
    isLoadingTokenizationRequests,
    refreshTokenizationRequests,
  } = useTokenizationRequestsByState(TokenizationRequestState.SELLING)

  // const [selectedLocation, setSelectedLocation] = useState(false)
  // const [selectedAcreage, setSelectedAcreage] = useState(false)
  // const [selectedPrice, setSelectedPrice] = useState(false)

  const handleRefresh = useCallback(() => {
    refreshTokenizationRequests()
  }, [refreshTokenizationRequests])

  const renderItem = useCallback(
    (item: TokenizationRequest) => (
      <GridSaleLiveRenderItem
        tokenizationRequest={item}
        key={item.id}
        style={styles.itemContainer}
      />
    ),
    []
  )

  // const handleLocationFilter = () => {
  //   setSelectedLocation(!selectedLocation)
  // }

  // const handleAcreageFilter = () => {
  //   setSelectedAcreage(!selectedAcreage)
  // }

  // const handlePriceFilter = () => {
  //   setSelectedPrice(!selectedPrice)
  // }

  return (
    <View style={styles.container}>
      {isLoadingTokenizationRequests ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : tokenizationRequests.length === 0 ? (
        <EmptyView />
      ) : (
        <>
          {/* <FilterButtons
            onPressLocation={handleLocationFilter}
            onPressAcreage={handleAcreageFilter}
            onPressPrice={handlePriceFilter}
            selectedLocation={selectedLocation}
            selectedAcreage={selectedAcreage}
            selectedPrice={selectedPrice}
          /> */}
          <ResultsHeader count={tokenizationRequests.length} />
          <FlatList
            data={tokenizationRequests}
            renderItem={({ item }) => renderItem(item)}
            keyExtractor={(item) => item.id.toString()}
            numColumns={2}
            contentContainerStyle={styles.flatListContent}
            columnWrapperStyle={styles.columnWrapper}
            refreshControl={
              <RefreshControl
                refreshing={isLoadingTokenizationRequests}
                onRefresh={handleRefresh}
                colors={[Colors.primary]}
              />
            }
          />
        </>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  flatListContent: {
    paddingTop: 8,
  },
  columnWrapper: {
    justifyContent: "space-between",
    marginBottom: 8,
    gap: itemSpacing,
  },
  itemContainer: {
    width: itemWidth,
  },
})

export default SaleLiveTab
