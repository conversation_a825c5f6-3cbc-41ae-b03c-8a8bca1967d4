import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import { getEstateRequests, TokenizationRequest } from "src/api"
import { TokenizationRequestState } from "src/api/types/estate"

/**
 * Hook to fetch tokenization requests filtered by state
 * @param state The state to filter by (e.g., SELLING, TRANSFERRING_OWNERSHIP)
 */
export const useTokenizationRequestsByState = (
  state: TokenizationRequestState
) => {
  const query = useQueryWithErrorHandling<TokenizationRequest[]>({
    queryKey: [...QueryKeys.ESTATE.REQUESTS, { state }],
    queryFn: () =>
      getEstateRequests({
        itemsPerPage: 100,
        currentPage: 0,
        states: state,
      }),
  })

  return {
    tokenizationRequests: query.data || [],
    isLoadingTokenizationRequests: query.isLoading,
    tokenizationRequestsError: query.error ? String(query.error) : null,
    refreshTokenizationRequests: query.refetch,
  }
}
