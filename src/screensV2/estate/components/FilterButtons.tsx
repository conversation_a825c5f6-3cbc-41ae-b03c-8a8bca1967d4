import React from "react"
import { Image, ImageRequireSource, StyleSheet, Text, View } from "react-native"
import { CustomPressable } from "src/componentsv2"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import downFillIcon from "assets/imagesV2/ic_down_fill.png"
import locationIcon from "assets/imagesV2/ic_location.png"
import scalingIcon from "assets/imagesV2/ic_scaling.png"
import cirleDollarSignIcon from "assets/imagesV2/ic_circle_dollar_sign.png"

interface FilterButtonProps {
  icon: ImageRequireSource
  label: string
  onPress: () => void
  selected?: boolean
}

const FilterButton: React.FC<FilterButtonProps> = ({
  icon,
  label,
  onPress,
  selected = false,
}) => {
  const itemColor = selected ? Colors.Primary500 : Colors.Neutral500
  return (
    <CustomPressable
      style={[styles.filterButton, selected && styles.selectedFilterButton]}
      onPress={onPress}
    >
      <Image
        source={icon}
        style={[viewStyles.size14Icon, { tintColor: itemColor }]}
      />
      <Text style={[styles.filterButtonText, { color: itemColor }]}>
        {label}
      </Text>
      <Image
        source={downFillIcon}
        style={[viewStyles.size12x8Icon, { tintColor: itemColor }]}
      />
    </CustomPressable>
  )
}

interface FilterButtonsProps {
  onPressLocation: () => void
  onPressAcreage: () => void
  onPressPrice: () => void
  selectedLocation?: boolean
  selectedAcreage?: boolean
  selectedPrice?: boolean
}

const FilterButtons: React.FC<FilterButtonsProps> = ({
  onPressLocation,
  onPressAcreage,
  onPressPrice,
  selectedLocation = false,
  selectedAcreage = false,
  selectedPrice = false,
}) => {
  return (
    <View style={styles.filterContainer}>
      <FilterButton
        icon={locationIcon}
        label="Dubai"
        onPress={onPressLocation}
        selected={selectedLocation}
      />
      <FilterButton
        icon={scalingIcon}
        label="Acreage"
        onPress={onPressAcreage}
        selected={selectedAcreage}
      />
      <FilterButton
        icon={cirleDollarSignIcon}
        label="Price per NFT"
        onPress={onPressPrice}
        selected={selectedPrice}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  filterContainer: {
    flexDirection: "row",
    marginBottom: 12,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 8,
  },
  selectedFilterButton: {
    backgroundColor: Colors.opacityPrimary15,
    borderColor: Colors.Primary500,
  },
  filterButtonText: {
    ...textStyles.MMedium,
    marginHorizontal: 6,
  },
})

export default FilterButtons
