import React from "react"
import { Image, StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { MarketplaceOffer } from "src/api"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import * as Routes from "src/navigatorV2/routes/RoutesV2"
import { useTranslation } from "react-i18next"
import { CustomPressable, CardView } from "src/componentsv2"
import { InfoItem, OfferState } from "../../shared/components"
import { calculateGridItemDimensions } from "src/utils/layout"
import { formatCurrency, formatNumericByDecimals } from "utils"
import { useCurrencies } from "../../shared/hooks/useCurrencies"
import icBox from "assets/imagesV2/ic_file_box.png"
import icFileBox from "assets/imagesV2/ic_file_box.png"
import icCircleCheck from "assets/imagesV2/ic_circle_check.png"
import icCircleX from "assets/imagesV2/ic_circle_x.png"
import icCart from "assets/imagesV2/ic_cart.png"
import { OfferActionButton } from "../../shared/components/OfferActionButton"

const { itemWidth } = calculateGridItemDimensions({
  numColumns: 2,
})

interface MarketplaceOfferGridItemProps {
  item: MarketplaceOffer
  style?: ViewStyle
}

const MarketplaceOfferGridItem: React.FC<MarketplaceOfferGridItemProps> = ({
  style,
  item,
}) => {
  const { t } = useTranslation()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const {
    state,
    sellingAmount,
    soldAmount,
    unitPrice,
    isDivisible,
    currency,
    estate: {
      id,
      decimals,
      metadata: {
        metadata: { name },
        imageUrl,
      },
    },
  } = item

  const { tokenSymbol } = useCurrencies(currency)

  const handlePress = () => {
    navigation.navigate(Routes.ESTATE_DETAIL, { estateId: id })
  }

  return (
    <CustomPressable onPress={handlePress} style={style}>
      <CardView style={styles.card}>
        <Image source={{ uri: imageUrl }} style={styles.image} />

        <View style={styles.contentContainer}>
          <Text style={styles.title} numberOfLines={2}>
            {name}
          </Text>
          <OfferState state={state} />

          <View style={styles.infoRow}>
            <InfoItem
              icon={icBox}
              text={`${formatCurrency(formatNumericByDecimals(unitPrice, decimals))} ${tokenSymbol} / ${t("NFT")}`}
            />
            <Text style={styles.dot}>•</Text>
            <InfoItem
              icon={icFileBox}
              text={formatCurrency(
                formatNumericByDecimals(sellingAmount, decimals)
              )}
            />
          </View>
          <View style={styles.infoRow}>
            <InfoItem
              icon={isDivisible ? icCircleCheck : icCircleX}
              text={t("Divisible")}
            />
            <Text style={styles.dot}>•</Text>
            <InfoItem
              icon={icCart}
              text={formatCurrency(
                formatNumericByDecimals(soldAmount, decimals)
              )}
            />
          </View>
        </View>
        <OfferActionButton offer={item} title={t("Buy now")} />
      </CardView>
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    flex: 1,
    width: itemWidth,
  },
  image: {
    borderRadius: 6,
    height: 100,
    resizeMode: "cover",
    margin: 6,
  },
  contentContainer: {
    flex: 1,
    padding: 6,
  },
  title: {
    ...textStyles.SMedium,
    color: Colors.PalleteWhite,
    marginBottom: 6,
  },
  infoRow: {
    marginBottom: 2,
    flexDirection: "row",
    alignItems: "center",
  },
  dot: {
    color: Colors.white,
    marginHorizontal: 3,
  },
  areaContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
})

export default MarketplaceOfferGridItem
