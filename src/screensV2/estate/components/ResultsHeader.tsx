import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { textStyles } from "src/config/styles"
// import menuWhiteIcon from "assets/imagesV2/ic_menu_white.png"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { formatCurrency } from "src/utils"
// import { CustomPressable } from "src/componentsv2"

interface ResultsHeaderProps {
  count: number
  onToggleView?: () => void
}

const ResultsHeader: React.FC<ResultsHeaderProps> = ({
  count,
  // onToggleView,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.container}>
      <Text style={styles.resultsText}>
        {t("Results matching you", { results: formatCurrency(count) })}
      </Text>
      {/* <CustomPressable onPress={onToggleView} style={styles.viewToggle}>
        <View style={styles.menuBackground}>
          <Image source={menuWhiteIcon} style={viewStyles.size16Icon} />
        </View>
      </CustomPressable> */}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  resultsText: {
    ...textStyles.size2XLMedium,
    color: Colors.white,
  },
  viewToggle: {
    padding: 4,
  },
  menuBackground: {
    borderColor: Colors.Neutral900,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
  },
})

export default ResultsHeader
