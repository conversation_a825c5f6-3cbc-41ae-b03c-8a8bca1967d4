import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import {
  Estate,
  getEstateRequests,
  getEstates,
  getMarketPlaceOffersFilter,
  getMortgageLoansFilter,
  MarketplaceOffer,
  MortgageTokenLoan,
  TokenizationRequest,
} from "src/api"

/**
 * Hook to fetch tokenization requests
 */
export const useTokenizationRequests = () => {
  return useQueryWithErrorHandling<TokenizationRequest[]>({
    queryKey: QueryKeys.ESTATE.REQUESTS,
    queryFn: () =>
      getEstateRequests({
        itemsPerPage: 100,
        currentPage: 0,
      }),
  })
}

/**
 * Hook to fetch estates
 */
export const useEstates = () => {
  return useQueryWithErrorHandling<Estate[]>({
    queryKey: QueryKeys.ESTATE.LIST,
    queryFn: () =>
      getEstates({
        itemsPerPage: 100,
        currentPage: 0,
      }),
  })
}

/**
 * Hook to fetch marketplace offers
 */
export const useMarketplaceOffers = (states?: string) => {
  return useQueryWithErrorHandling<MarketplaceOffer[]>({
    queryKey: QueryKeys.MARKETPLACE.OFFERS(),
    queryFn: () =>
      getMarketPlaceOffersFilter({
        itemsPerPage: 100,
        currentPage: 0,
        states,
      }),
  })
}

/**
 * Hook to fetch mortgage loans
 */
export const useMortgageLoans = () => {
  return useQueryWithErrorHandling<MortgageTokenLoan[]>({
    queryKey: QueryKeys.MORTGAGE.LOANS(),
    queryFn: () =>
      getMortgageLoansFilter({
        itemsPerPage: 100,
        currentPage: 0,
      }),
  })
}
