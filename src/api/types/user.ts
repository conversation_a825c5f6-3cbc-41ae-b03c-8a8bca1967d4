export type User = {
  id: string
  address: string
  email: string
  phone: string
  avatarUrl?: string
  isBroker: boolean
  isModerator: boolean
  isManager: boolean
  isVerified: boolean
  nationality: string
  dob: number
  alias: string
  status: string
}

export type PublicUser = {
  id: string
  address: string
  cicNationality: string
  cicFullName: string
  avatarUrl?: string
}

export type AdminUpdateUserPayload = {
  status: "VERIFIED"
}

export type ContactPayload = {
  fullName: string
  message: string
  email: string
}
