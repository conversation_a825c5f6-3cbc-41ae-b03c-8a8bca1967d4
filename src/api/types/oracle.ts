export enum InvestmentRoundType {
  BACKER = "BACKER",
  SEED = "SEED",
  PRIVATE_SALE_1 = "PRIVATE_SALE_1",
  PRIVATE_SALE_2 = "PRIVATE_SALE_2",
  PUBLIC_SALE = "PUBLIC_SALE",
}

export enum InvestmentRoundState {
  PENDING = "PENDING",
  PROGRESSING = "PROGRESSING",
  FINISHED = "FINISHED",
}

export type Distribution = {
  distributionId: string
  totalAmount: string
  receiverAddress: string
  vestingEndsAtInSeconds: number
  withdrawnAmount: number
  distributeAt: number
  isStaked: boolean
}

export type Investment = {
  affiliation: string
  investorName: string
  address: string
  amount: number
  totalAmount: number
  unlockedAmount: number
  withdrawnAmount: number
  distributions?: Distribution[]
}

export type InvestmentRound = {
  round: InvestmentRoundType
  state: InvestmentRoundState
  tokenAllocation: string
  investments: Investment[]
}

export type InvestmentProperties = {
  rounds: InvestmentRound[]
}

export enum TreasuryType {
  BACKER = "BACKER",
  SEED = "SEED",
  PRIVATE_SALE_1 = "PRIVATE_SALE_1",
  PRIVATE_SALE_2 = "PRIVATE_SALE_2",
  PUBLIC_SALE = "PUBLIC_SALE",
  MARKET_MAKER = "MARKET_MAKER",
  CORE_TEAM = "CORE_TEAM",
  EXTERNAL_TREASURY = "EXTERNAL_TREASURY",
  STAKING_REWARD = "STAKING_REWARD",
}

export interface TokenAllocation {
  treasuryType: TreasuryType
  allocation: number
  minted: boolean
  percentage: number
  sold: number
  unlocked: number
}

export interface TreasuryProperties {
  totalSupply: number
  sold: number
  unlocked: number
  liquidity: number
  tokenAllocations: TokenAllocation[]
}

export type Oracle = {
  investment: InvestmentProperties
  treasury: TreasuryProperties
}
