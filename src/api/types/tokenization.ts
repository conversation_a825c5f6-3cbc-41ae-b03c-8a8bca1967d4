import {
  ApplicationStatus,
  TokenizationRequest,
  UserBrief,
} from "src/api/types/index"

export type TransactionBrief = {
  hash: string
  blockNumber: string
  timestamp: number
  from: string
  to: string
}

export type TokenizationDepositor = {
  tokenAmount: string
  depositAmount: string
  hasWithdrawn: boolean
  request: TokenizationRequest
  depositor: UserBrief
}

export type TokenizationDeposit = {
  id: number
  amount: string
  value: string
  transaction: TransactionBrief
  request: TokenizationRequest
  depositor: UserBrief
}

export type MyTokenization = {
  id: string
  imageUrl: string
  decimals: number
  name: string
  status: ApplicationStatus
  totalSupply: string
  initialPrice: string
  maxSellingAmount: string
  minSellingAmount: string
  soldAmount: string
  currency: string
  applicationId: number
  requestId: string
  estateId: string
}

export enum TokenizationStatus {
  APPLICATION_VALIDATING = "APPLICATION_VALIDATING",
  APPLICATION_CANCELLED = "APPLICATION_CANCELLED",
  REQUEST_SELLING = "REQUEST_SELLING",
  REQUEST_CANCELLED = "REQUEST_CANCELLED",
  REQUEST_CONFIRMED = "REQUEST_CONFIRMED",
  REQUEST_INSUFFICIENT_SOLD_AMOUNT = "REQUEST_INSUFFICIENT_SOLD_AMOUNT",
  REQUEST_TRANSFERRING_OWNERSHIP = "REQUEST_TRANSFERRING_OWNERSHIP",
  REQUEST_EXPIRED = "REQUEST_EXPIRED",
}
