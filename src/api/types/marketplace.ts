import { Estate, PublicUser } from "src/api/types/index"

export enum MarketplaceOfferState {
  SELLING = "SELLING",
  SOLD = "SOLD",
  CANCELLED = "CANCELLED",
}

export type MarketplaceOffer = {
  id: string
  sellingAmount: string
  soldAmount: string
  unitPrice: string
  currency: string
  isDivisible: boolean
  state: MarketplaceOfferState
  estate: Estate
  seller: PublicUser
}

export type Transaction = {
  hash: string
  blockNumber: string
  timestamp: string
  from: string
  to: string
}

export type FatOffer = Omit<MarketplaceOffer, "seller"> & { estate: Estate }
