export type SignInRequest = {
  nonce: string
  address: string
  signature: string
}

export type SignInResponse = {
  token: string
  tokenExpiredTimeInSeconds: number
  refreshToken?: string
  refreshTokenExpiredTimeInSeconds?: number
}

export type TokenDecoded = {
  address: string
  isManager: boolean
  isModerator: boolean
  iss: string
  sub: string
  exp: number
  nbf: number
  iat: number
}
