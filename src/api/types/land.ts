import { Loan, PublicUser, User } from "src/api/types/index"

export type LandType =
  | "RESIDENTIAL"
  | "PRODUCTION"
  | "AGRICULTURAL"
  | "NON_AGRICULTURAL"
  | "FOREST"
  | "PERENNIAL_CROP"
  | "INDUSTRIAL"

export type EstateState =
  | "PENDING"
  | "SELLING"
  | "TRANSFERING_OWNERSHIP"
  | "TOKENIZED"
  | "CANCELLED"

export type Land = {
  id: string
  tokenId: string
  requestId: string
  tokenizationRequestId: string
  nftChainAddress: string
  userId: string
  name: string
  address: string
  addressCodeLevel1: string
  addressCodeLevel2: string
  addressCodeLevel3: string
  area: string
  category: string
  status: string
  unitPrice: string
  currencyId: string
  minSellingAmount: string
  maxSellingAmount: string
  totalSupply: number
  description: string
  expiredAt: string
  tokenizationRequestErrorCode: string
  createdAt: string
  updatedAt: string
  uri: string
  metadata: {
    attributes: { traitType: string; value: string }[]
  }
  photos: {
    id: string
    category: "CERTIFICATE_OF_USE_RIGHTS" | "ITSELF"
    landId: string
    fileId: string
    createdAt: string
    updatedAt: string
    url: string
  }[]
}

export type LandTable = {
  id: number
  name: string

  tokenizationRequestId: number
  nftChainAddress: string
  ownerChainAddress: string
  address: string
  addressCodeLevel1: string
  addressCodeLevel2: string
  addressCodeLevel3: string
  area: number
  type: LandType
  status: EstateState
  priceWei: string
  priceUnitChainAddress: string
  minSellingAmountWei: string
  maxSellingAmountWei: string
  description: string
}

export type AdminUpdateLandPayload = {
  status: "INVALIDATED"
}

export type Holder = {
  id: string
  amount: string
  tokenId: string
  user: PublicUser
  createdAt: string
  updatedAt: string
}

export type Depositor = {
  id: string
  depositedAmount: string
  tokenId: string
  userId: string
  createdAt: string
  updatedAt: string
  user: User
}

export type FatLoan = Omit<Loan, "borrower"> & { land: Land }
export type StartLettingType = {
  proposalId: string
  duration: number
  currencyId?: string
}
export type CancelLettingType = {
  proposalId: string
  duration: number
}
export type LandCategoryType =
  | "RESIDENTIAL"
  | "PRODUCTION"
  | "AGRICULTURAL"
  | "NON_AGRICULTURAL"
  | "FOREST"
  | "PERENNIAL_CROP"
  | "INDUSTRIAL"
export type ChangeUsageType = {
  proposalId: string
  duration: number
  newUsage: LandCategoryType
}
export type ExtendExpirationType = {
  proposalId: string
  duration: number
  extendYears: number
}
export type ExtractLandType = {
  proposalId: string
  currencyId: string
  duration: number
  paymentId: string
  value: string
}
export type ProposalMetadataType = {
  startLetting?: StartLettingType
  cancelLetting?: CancelLettingType
  changeUsage?: ChangeUsageType
  extendExpiration?: ExtendExpirationType
  extract?: ExtractLandType
}
export type ProposalDao = {
  id: string
  proposalId: string
  tokenId: string
  budget: string
  fund: string
  totalApproval: string
  totalDisapproval: string
  extraData: number
  label:
    | "START_LETTING"
    | "CANCEL_LETTING"
    | "CHANGE_USAGE"
    | "EXTEND_EXPIRATION"
    | "EXTRACT"
  state:
    | "PENDING"
    | "DISQUALIFIED"
    | "VOTING"
    | "FAILED"
    | "PASSED"
    | "CONFIRMED"
  startAt: number
  endAt: number
  proposerAddress: string
  metadata: ProposalMetadataType
}
