import { z } from "zod"

export const hrmReferralRoundEnum = z.enum([
  "BACKER",
  "SEED",
  "PRIVATE_SALE_1",
  "PRIVATE_SALE_2",
  "PUBLIC_SALE",
])

export const hrmReferralStatusEnum = z.enum([
  "VALIDATING",
  "WHITELISTING",
  "WHITELISTED",
])

export type HrmReferralRoundType = z.infer<typeof hrmReferralRoundEnum>

export type HrmReferralStatusType = z.infer<typeof hrmReferralStatusEnum>

export type Referral = {
  id: string
  chainAddress: string
  referralCode: string
  hrmUserId: string
  round: HrmReferralRoundType
  status: HrmReferralStatusType
  packageAmount: string
  createdAt: string
  updatedAt: string
}
