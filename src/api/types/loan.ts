import { Estate, PublicUser, User } from "src/api/types/index"

export type LoanStateType =
  | "PENDING"
  | "APPROVED"
  | "REPAID"
  | "FORECLOSED"
  | "CANCELLED"

export type Loan = {
  id: string
  tokenId: string
  borrowerAddress: string
  lenderAddress?: string
  mortgageAmount: string
  principal: string
  repayment: string
  currency: string
  duration: number
  due?: string
  state: LoanStateType
  borrower: User
}

export enum MortgageTokenLoanState {
  PENDING = "PENDING",
  SUPPLIED = "SUPPLIED",
  OVERDUE = "OVERDUE",
  REPAID = "REPAID",
  FORECLOSED = "FORECLOSED",
  CANCELLED = "CANCELLED",
}

export type MortgageTokenLoan = {
  id: string
  mortgageAmount: string
  currency: string
  principal: string
  repayment: string
  state: MortgageTokenLoanState
  durationInSeconds: number
  dueInSeconds: number
  estate: Estate
  owner: PublicUser
  borrower: PublicUser
  lender: PublicUser
}

export type MortgageTokenLoanFilter = {
  estateId?: string
  owner?: string
  borrower?: string
  lender?: string
  states?: string
}
