import { MarketplaceOffer } from "src/api/types/marketplace"
import { UserBrief } from "src/api/types/estate"
import { TransactionBrief } from "src/api/types/tokenization"

export type MarketplaceTokenSale = {
  id: number
  amount: string
  value: string
  royaltyReceiver: string
  royaltyAmount: string
  commissionAmount: string
  transaction: TransactionBrief
  offer: MarketplaceOffer
  buyer: UserBrief
  seller: UserBrief
}

export type MarketplaceTokenSaleFilter = {
  id: string
  tokenId: string
  offerId: string
  sellerAddress: string
  buyerAddress: string
  estateId: string
}
