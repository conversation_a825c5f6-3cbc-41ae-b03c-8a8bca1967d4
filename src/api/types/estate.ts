export type EstateTokenBalance = {
  account: UserBrief
  value: string
}

export enum ApplicationType {
  PUBLIC_SALE = "PUBLIC_SALE",
}

export enum TokenizationRequestState {
  CONFIRMED = "CONFIRMED",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED",
  SELLING = "SELLING",
  INSUFFICIENT_SOLD_AMOUNT = "INSUFFICIENT_SOLD_AMOUNT",
  TRANSFERRING_OWNERSHIP = "TRANSFERRING_OWNERSHIP",
}

export enum TokenizationState {
  TOKENIZED = "TOKENIZED",
}

export enum EstateZone {
  VIETNAM = "VIETNAM",
}

export enum CredentialType {
  VN001 = "VN001",
}

export enum ApplicationStatus {
  APPLICATION_VALIDATING = "APPLICATION_VALIDATING",
  APPLICATION_CANCELLED = "APPLICATION_CANCELLED",
  REQUEST_SELLING = "REQUEST_SELLING",
  REQUEST_CANCELLED = "REQUEST_CANCELLED",
  REQUEST_EXPIRED = "REQUEST_EXPIRED",
  REQUEST_CONFIRMED = "REQUEST_CONFIRMED",
  REQUEST_INSUFFICIENT_SOLD_AMOUNT = "REQUEST_INSUFFICIENT_SOLD_AMOUNT",
  REQUEST_TRANSFERRING_OWNERSHIP = "REQUEST_TRANSFERRING_OWNERSHIP",
}

export type LocaleDetail = {
  zone: EstateZone
  vietnam: {
    level1: string
    level2: string
    level3: string
  }
}

export type FileSchema = {
  id: number
  created_at_in_seconds: number
  name: string
  content_type: string
  path: string
  bucket: string
}

export type EstateTokenAttribute = {
  trait_type: string
  value: string
}

export enum TraitType {
  CREDENTIAL_TYPE = "Credential Type",
  CITY_PROVINCE = "City/Province",
  DISTRICT = "District",
  WARD = "Ward",
  STREET = "Street",
  LOCALE_DETAILS = "Locale Details",
  ROAD_ACCESS = "Road Access",
  CATEGORY = "Category",
  AREA_201_300 = "201 - 300",
  BUILT_SQM = "Built Sqm",
  YEAR_BUILT = "Year Built",
  CONSTRUCTION_CLASS = "Construction Class",
  FLOORS = "Floor(s)",
  ROOMS = "Room(s)",
  BEDROOMS = "Bedroom(s)",
  BATHROOMS = "Bathroom(s)",
  KITCHENS = "Kitchen(s)",
  LIVING_ROOMS = "Living room(s)",
  LANDSCAPE = "Landscape",
  FEATURE = "Feature",
  NEARBY = "Nearby",
  Nation = "Nation",
}

export enum EstateTokenAreaUnit {
  SQM = "sqm",
  SQFT = "sqft",
}

export enum LandRegistryOfficeType {
  LAWYER = "LAWYER",
}

export type EstateTokenArea = {
  area: number
  unit: EstateTokenAreaUnit
}

export type EstateMetadata = {
  id: number
  broker_address?: string
  uri: string
  name: string
  address: string
  description: string
  locale_detail: LocaleDetail
  image: string
  image_file: FileSchema
  credential_id: string
  credential_type: CredentialType
  credential_photos: FileSchema[]
  estate_photos: FileSchema[]
  attributes: EstateTokenAttribute[]
  area: EstateTokenArea
  status: ApplicationStatus
}

export type ExtendedMetadata = {
  metadata: EstateMetadata
  imageUrl: string
  credentialPhotoUrls: string[]
  estatePhotoUrls: string[]
}

export type UserBrief = {
  address: string
  alias: string
  name: string
  nationality: string
  isVerified: boolean
  avatarUrl: string
}

export type Application = {
  id: number
  broker?: string
  createdAtInSeconds: number
  type: ApplicationType
  tokenizationRequestId: string
  requesterAddress: string
  metadataId: number
  totalSupply: string
  minSellingAmount: string
  maxSellingAmount: string
  unitPrice: string
  currency: string
  decimals: number
  expireAtInSeconds: number
  durationInSeconds: number
  isCancelled: boolean
  cancellationReason: string
  state: TokenizationRequestState
  requester: UserBrief
  metadata: ExtendedMetadata
}
export type ApplicationRequestMetadata = {
  id: number
  created_at: number
  updated_at: number
  deleted_at: number
  uri: string
  name: string
  address: string
  description: string
  locale_detail: LocaleDetail
  image: string
  image_file: FileSchema
  credential_id: string
  credential_type: CredentialType
  credential_photos: FileSchema[]
  estate_photos: FileSchema[]
  attributes: EstateTokenAttribute[]
  area: EstateTokenArea
  status: ApplicationStatus
  broker_address: string
  land_registry_office_id: string
}

export type RequestMetadata = {
  metadata: ApplicationRequestMetadata
  imageUrl: string
  credentialPhotoUrls: string[]
  estatePhotoUrls: string[]
  landRegistryDocumentUrls: string[]
}

export type TokenizationRequest = {
  id: string
  broker?: string
  estateId: string
  uri: string
  tokenMintEventTxHash: string
  totalSupply: string
  minSellingAmount: string
  maxSellingAmount: string
  soldAmount: string
  unitPrice: string
  currency: string
  decimals: number
  expireAtInSeconds: number
  publicSaleEndsAtInSeconds: number
  state: TokenizationRequestState
  requester: UserBrief
  metadata: RequestMetadata
}

export type Estate = {
  id: string
  uri: string
  totalSupply: string
  decimals: number
  tokenMintEventTxHash: string
  createAtInSeconds: number
  expireAtInSeconds: number
  isDeprecated: boolean
  brokerAddress: string
  tokenizationRequest: TokenizationRequest
  metadata: RequestMetadata
}

export type EstateTokenEstateBrief = {
  id: string
  uri: string
  totalSupply: string
  decimals: number
  createAtInSeconds: number
  expireAtInSeconds: number
  isDeprecated: boolean
  brokerAddress: string
  metadata: RequestMetadata
}

export type RegistryOffice = {
  id: string
  name: string
  type: LandRegistryOfficeType
}

export type MyEstate = {
  id: string
  totalSupply: string
  imageUrl: string
  name: string
  balance: string
  initialUnitPrice: string
  decimals: number
  currency: string
}

export const getFullAddress = (
  address: string,
  locale_detail: LocaleDetail
) => {
  return `${address}, ${locale_detail.vietnam.level3}, ${locale_detail.vietnam.level2}, ${locale_detail.vietnam.level1} `
}

export enum RequirementType {
  // Chứng nhận quyền sỡ hữu/sử dụng bất động sản
  OWNERSHIP_CERTIFICATE = "OWNERSHIP_CERTIFICATE",
  // Xác minh danh tính công ty
  COMPANY_IDENTITY_VERIFICATION = "COMPANY_IDENTITY_VERIFICATION",
  // Xác minh danh tính chủ pháp nhân tại Việt Nam
  LEGAL_REPRESENTATIVE_VERIFICATION = "LEGAL_REPRESENTATIVE_VERIFICATION",
  // Xác minh mối liên hệ với công ty mẹ
  PARENT_COMPANY_RELATIONSHIP_VERIFICATION = "PARENT_COMPANY_RELATIONSHIP_VERIFICATION",
  // Xác minh danh tính chủ bất động sản
  OWNER_IDENTITY_VERIFICATION = "OWNER_IDENTITY_VERIFICATION",
  // Chuyển nhượng bất động sản từ chủ bất động sản sang cho công ty
  TRANSFER_FROM_OWNER = "TRANSFER_FROM_OWNER",
  // Niêm phong bất động sản
  PROPERTY_SEALING = "PROPERTY_SEALING",
  // Thẩm định pháp lý bất động sản
  LEGAL_ASSESSMENT = "LEGAL_ASSESSMENT",
  // Tình trạng hôn thú của chủ bất động sản
  MARITAL_STATUS = "MARITAL_STATUS",
  // Định giá bất động sản
  ESTATE_VALUATION = "ESTATE_VALUATION",
  // Thuế thu nhập cá nhân
  INCOME_TAX = "INCOME_TAX",
  // Thuế giá trị gia tăng
  VAT = "VAT",
  // Phí trước bạ
  REGISTRATION_FEE = "REGISTRATION_FEE",
  // Thuế tài nguyên
  RESOURCE_TAX = "RESOURCE_TAX",
  // Cập nhật chứng nhận quyền sở hữu/sử dụng bất động sản
  CERTIFICATE_UPDATE = "CERTIFICATE_UPDATE",
  // Xác nhận tình trạng thế chấp
  MORTGAGE_STATUS = "MORTGAGE_STATUS",
  // Chuyển nhượng bất động sản từ đơn vị nhận thế chấp sang cho công ty
  TRANSFER_FROM_MORTGAGEE = "TRANSFER_FROM_MORTGAGEE",
  // Giải chấp
  MORTGAGE_RELEASE = "MORTGAGE_RELEASE",
}

export type Issuer = {
  id: number
  address: string
  name: string
  type: LandRegistryOfficeType
}

export type LegalRequirement = {
  requirementType: RequirementType
  fileUrl: string
  fileName: string
  id: number
  metadataID: number
  issuer: Issuer
}
