import { useReadContract } from "wagmi"
import {
  CONTRACT_ADDRESS_ESTATE_FORGER,
  CONTRACT_ADDRESS_ESTATE_TOKEN,
} from "src/config/env"
import { estateForgerAbi } from "./estate-forger"
import { estateTokenAbi } from "./stake-token"

export const useCollectionHasWithdrawn = (
  requestId: string,
  userAddress: string
): boolean => {
  const { data } = useReadContract({
    abi: estateForgerAbi,
    address: CONTRACT_ADDRESS_ESTATE_FORGER,
    functionName: "hasWithdrawn",
    args: [BigInt(requestId), userAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  return Boolean(data)
}

export const useCollectionDepositedAmount = (
  requestId: string,
  userAddress: string
): bigint => {
  const { data } = useReadContract({
    abi: estateForgerAbi,
    address: CONTRACT_ADDRESS_ESTATE_FORGER,
    functionName: "deposits",
    args: [BigInt(requestId), userAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return 0n
  return data as bigint
}

export const useCollectionIsApprovedForAll = (
  userAddress: string,
  contractAddress: string
): boolean => {
  const { data } = useReadContract({
    abi: estateTokenAbi,
    address: CONTRACT_ADDRESS_ESTATE_TOKEN,
    functionName: "isApprovedForAll",
    args: [userAddress, contractAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  return Boolean(data)
}

export type TokenizationRequestContract = {
  requestId: bigint
  tokenId: bigint
  uri: string
  totalSupply: bigint
  minSellingAmount: bigint
  maxSellingAmount: bigint
  soldAmount: bigint
  unitPrice: bigint
  currency: string
  expireAt: number
  publicSaleEndsAt: number
  requester: string
}
