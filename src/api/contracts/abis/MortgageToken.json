{"_format": "hh-sol-artifact-1", "contractName": "IMortgageToken", "sourceName": "contracts/lend/interfaces/IMortgageToken.sol", "abi": [{"inputs": [], "name": "InsufficientFunds", "type": "error"}, {"inputs": [], "name": "InvalidCancelling", "type": "error"}, {"inputs": [], "name": "InvalidCurrency", "type": "error"}, {"inputs": [], "name": "InvalidEstateId", "type": "error"}, {"inputs": [], "name": "InvalidForeclosing", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidLending", "type": "error"}, {"inputs": [], "name": "InvalidLoanId", "type": "error"}, {"inputs": [], "name": "InvalidMortgageAmount", "type": "error"}, {"inputs": [], "name": "Invalid<PERSON><PERSON>cipal", "type": "error"}, {"inputs": [], "name": "InvalidRate", "type": "error"}, {"inputs": [], "name": "InvalidRepaying", "type": "error"}, {"inputs": [], "name": "InvalidRepayment", "type": "error"}, {"inputs": [], "name": "InvalidUpdating", "type": "error"}, {"inputs": [], "name": "Overdue", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "approved", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "newValue", "type": "string"}], "name": "BaseURIUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "_fromTokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_toTokenId", "type": "uint256"}], "name": "BatchMetadataUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "FeeRateUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "loanId", "type": "uint256"}], "name": "LoanCancellation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "loanId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}], "name": "LoanForeclosure", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "loanId", "type": "uint256"}], "name": "LoanRepayment", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "MetadataUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "loanId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "estateId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "borrower", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "mortgageAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "principal", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "repayment", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "currency", "type": "address"}, {"indexed": false, "internalType": "uint40", "name": "duration", "type": "uint40"}], "name": "NewLoan", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "lender", "type": "address"}, {"indexed": false, "internalType": "uint40", "name": "due", "type": "uint40"}, {"indexed": false, "internalType": "uint256", "name": "feeAmount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "commissionReceiver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "commissionAmount", "type": "uint256"}], "name": "NewToken", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "RoyaltyRateUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "estateId", "type": "uint256"}, {"internalType": "uint256", "name": "mortgageAmount", "type": "uint256"}, {"internalType": "uint256", "name": "principal", "type": "uint256"}, {"internalType": "uint256", "name": "repayment", "type": "uint256"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "uint40", "name": "duration", "type": "uint40"}], "name": "borrow", "outputs": [{"internalType": "uint256", "name": "loanId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "loanId", "type": "uint256"}], "name": "cancel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "estateToken", "outputs": [{"internalType": "address", "name": "estateToken", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "loanId", "type": "uint256"}], "name": "exists", "outputs": [{"internalType": "bool", "name": "existence", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeReceiver", "outputs": [{"internalType": "address", "name": "feeReceiver", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "loanId", "type": "uint256"}], "name": "foreclose", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getApproved", "outputs": [{"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getFeeRate", "outputs": [{"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct ICommon.Rate", "name": "rate", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "loanId", "type": "uint256"}], "name": "getLoan", "outputs": [{"components": [{"internalType": "uint256", "name": "estateId", "type": "uint256"}, {"internalType": "uint256", "name": "mortgageAmount", "type": "uint256"}, {"internalType": "uint256", "name": "principal", "type": "uint256"}, {"internalType": "uint256", "name": "repayment", "type": "uint256"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "uint40", "name": "due", "type": "uint40"}, {"internalType": "enum IMortgageToken.LoanState", "name": "state", "type": "uint8"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "address", "name": "lender", "type": "address"}], "internalType": "struct IMortgageToken.Loan", "name": "loan", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRoyaltyRate", "outputs": [{"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct ICommon.Rate", "name": "rate", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "loanId", "type": "uint256"}, {"internalType": "uint256", "name": "estateId", "type": "uint256"}], "name": "lend", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "loanNumber", "outputs": [{"internalType": "uint256", "name": "loanNumber", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "values", "type": "uint256[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onERC1155BatchReceived", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onERC1155Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ownerOf", "outputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "loanId", "type": "uint256"}], "name": "repay", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "salePrice", "type": "uint256"}], "name": "royaltyInfo", "outputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "royaltyAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "transferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "version", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}