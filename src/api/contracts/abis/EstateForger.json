{"_format": "hh-sol-artifact-1", "contractName": "IEstateForger", "sourceName": "contracts/land/interfaces/IEstateForger.sol", "abi": [{"inputs": [], "name": "AlreadyHadDepositor", "type": "error"}, {"inputs": [], "name": "AlreadyWithdrawn", "type": "error"}, {"inputs": [], "name": "Cancelled", "type": "error"}, {"inputs": [], "name": "FailedOwnershipTransfer", "type": "error"}, {"inputs": [], "name": "InsufficientFunds", "type": "error"}, {"inputs": [], "name": "InvalidCurrency", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidPriceFeedData", "type": "error"}, {"inputs": [], "name": "InvalidRate", "type": "error"}, {"inputs": [], "name": "InvalidRequestId", "type": "error"}, {"inputs": [], "name": "InvalidUnitPrice", "type": "error"}, {"inputs": [], "name": "InvalidUpdating", "type": "error"}, {"inputs": [], "name": "InvalidWithdrawing", "type": "error"}, {"inputs": [], "name": "MaxSellingAmountExceeded", "type": "error"}, {"inputs": [], "name": "MissingCurrencyRate", "type": "error"}, {"inputs": [], "name": "NotEnoughSoldAmount", "type": "error"}, {"inputs": [], "name": "SaleEnded", "type": "error"}, {"inputs": [], "name": "StalePriceFeed", "type": "error"}, {"inputs": [], "name": "StillSelling", "type": "error"}, {"inputs": [], "name": "Tokenized", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "baseMinUnitPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "baseMaxUnitPrice", "type": "uint256"}], "name": "BaseUnitPriceRangeUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "currency", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "rateValue", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "rateDecimals", "type": "uint8"}], "name": "DefaultRateUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "depositor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "withdrawer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "DepositWithdrawal", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "FeeRateUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "requester", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "zone", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "uri", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "minSellingAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "maxSellingAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "currency", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "decimals", "type": "uint8"}, {"indexed": false, "internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"indexed": false, "internalType": "uint40", "name": "endAt", "type": "uint40"}], "name": "NewRequest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "currency", "type": "address"}, {"indexed": false, "internalType": "address", "name": "feed", "type": "address"}, {"indexed": false, "internalType": "uint40", "name": "heartbeat", "type": "uint40"}], "name": "PriceFeedUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "RequestCancellation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "estateId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "soldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "feeAmount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "commissionReceiver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "commissionAmount", "type": "uint256"}], "name": "RequestConfirmation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "uri", "type": "string"}], "name": "RequestURIUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "requester", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "zone", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "uri", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "minSellingAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "maxSellingAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "currency", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "decimals", "type": "uint8"}, {"indexed": false, "internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"indexed": false, "internalType": "uint40", "name": "publicSaleEndAt", "type": "uint40"}], "name": "RequestUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "withdrawer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokenWithdrawal", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "currency", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "rateValue", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "rateDecimals", "type": "uint8"}], "name": "UnitPriceValidation", "type": "event"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenizationId", "type": "uint256"}, {"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "at", "type": "uint256"}], "name": "allocationOfAt", "outputs": [{"internalType": "uint256", "name": "allocation", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "baseMaxUnitPrice", "outputs": [{"internalType": "uint256", "name": "baseMaxUnitPrice", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "baseMinUnitPrice", "outputs": [{"internalType": "uint256", "name": "baseMinUnitPrice", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "cancelRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "commissionToken", "outputs": [{"internalType": "address", "name": "commissionToken", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "commissionReceiver", "type": "address"}], "name": "confirmRequest", "outputs": [{"internalType": "uint256", "name": "estateId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "depositor", "type": "address"}], "name": "deposits", "outputs": [{"internalType": "uint256", "name": "deposit", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeReceiver", "outputs": [{"internalType": "address", "name": "feeReceiver", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "currency", "type": "address"}], "name": "getDefaultRate", "outputs": [{"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct ICommon.Rate", "name": "rate", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getFeeRate", "outputs": [{"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct ICommon.Rate", "name": "rate", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "currency", "type": "address"}], "name": "getPriceFeed", "outputs": [{"components": [{"internalType": "address", "name": "feed", "type": "address"}, {"internalType": "uint40", "name": "heartbeat", "type": "uint40"}], "internalType": "struct IEstateForger.PriceFeed", "name": "priceFeed", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "getRequest", "outputs": [{"components": [{"internalType": "uint256", "name": "estateId", "type": "uint256"}, {"internalType": "bytes32", "name": "zone", "type": "bytes32"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "minSellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxSellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "soldAmount", "type": "uint256"}, {"internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"internalType": "uint40", "name": "closeAt", "type": "uint40"}, {"internalType": "address", "name": "requester", "type": "address"}], "internalType": "struct IEstateForger.Request", "name": "request", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasWithdrawn", "outputs": [{"internalType": "bool", "name": "hasWithdrawn", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "requestNumber", "outputs": [{"internalType": "uint256", "name": "requestNumber", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "requester", "type": "address"}, {"internalType": "bytes32", "name": "zone", "type": "bytes32"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "minSellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxSellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"internalType": "uint40", "name": "duration", "type": "uint40"}], "name": "requestTokenization", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "version", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "withdrawDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "withdrawToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}