{"_format": "hh-sol-artifact-1", "contractName": "IEstateMarketplace", "sourceName": "contracts/land/interfaces/IEstateMarketplace.sol", "abi": [{"inputs": [], "name": "InsufficientFunds", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidBuying", "type": "error"}, {"inputs": [], "name": "InvalidCancelling", "type": "error"}, {"inputs": [], "name": "InvalidCurrency", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidOfferId", "type": "error"}, {"inputs": [], "name": "InvalidRate", "type": "error"}, {"inputs": [], "name": "InvalidSellingAmount", "type": "error"}, {"inputs": [], "name": "InvalidTokenId", "type": "error"}, {"inputs": [], "name": "InvalidUnitPrice", "type": "error"}, {"inputs": [], "name": "InvalidUpdating", "type": "error"}, {"inputs": [], "name": "NotEnoughTokensToSell", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "offerId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "sellingAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "currency", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "isDivisible", "type": "bool"}], "name": "NewOffer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "offerId", "type": "uint256"}], "name": "OfferCancellation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "offerId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "royaltyReceiver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "royaltyAmount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "commissionReceiver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "commissionAmount", "type": "uint256"}], "name": "OfferSale", "type": "event"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offerId", "type": "uint256"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "buy", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offerId", "type": "uint256"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "buy", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offerId", "type": "uint256"}], "name": "cancel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "commissionToken", "outputs": [{"internalType": "address", "name": "commissionToken", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "estateToken", "outputs": [{"internalType": "address", "name": "estateToken", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offerId", "type": "uint256"}], "name": "get<PERSON>ffer", "outputs": [{"components": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "sellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "soldAmount", "type": "uint256"}, {"internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "bool", "name": "isDivisible", "type": "bool"}, {"internalType": "enum IEstateMarketplace.OfferState", "name": "state", "type": "uint8"}, {"internalType": "address", "name": "seller", "type": "address"}], "internalType": "struct IEstateMarketplace.Offer", "name": "offer", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "sellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "bool", "name": "isDivisible", "type": "bool"}], "name": "list", "outputs": [{"internalType": "uint256", "name": "offerId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "offerNumber", "outputs": [{"internalType": "uint256", "name": "offerNumber", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "version", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}