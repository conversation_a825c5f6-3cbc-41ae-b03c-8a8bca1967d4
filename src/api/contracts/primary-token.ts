import primaryTokenJson from "./abis/PrimaryToken.json"
import { useReadContract } from "wagmi"
import { CONTRACT_ADDRESS_PRIMARY_TOKEN } from "src/config/env"
import { Undefinable } from "src/api/types"

export const primaryTokenAbi = primaryTokenJson.abi

export const useBackerRoundContribution = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "backerRoundContribution",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const useBackerRoundUnlockedAmount = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "backerRoundUnlockedAmount",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const useCoreTeamTokensUnlocked = (): boolean => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "coreTeamTokensUnlocked",
    query: {
      refetchInterval: 15000,
    },
  })
  return !!data
}

export const usePreICOUnlockedAmount = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "preICOUnlockedAmount",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const useExternalTreasuryContribution = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "externalTreasuryContribution",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const useExternalTreasuryTokensUnlocked = (): boolean => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "externalTreasuryTokensUnlocked",
    query: {
      refetchInterval: 15000,
    },
  })
  return !!data
}

export const useMarketMakerContribution = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "marketMakerContribution",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const useMarketMakerTokensUnlocked = (): boolean => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "marketMakerTokensUnlocked",
    query: {
      refetchInterval: 15000,
    },
  })
  return !!data
}
export const useAuctionTokensUnlocked = (): boolean => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "auctionTokensUnlocked",
    query: {
      refetchInterval: 15000,
    },
  })
  return !!data
}

export const useSeedRoundContribution = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "seedRoundContribution",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const useSeedRoundUnlockedAmount = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "seedRoundUnlockedAmount",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const usePrivateSaleContribution = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "privateSaleContribution",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const usePrivateSaleUnlockedAmount = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "privateSaleUnlockedAmount",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const usePrimaryTokenUnlockAt = (): Undefinable<number> => {
  const { data } = useReadContract({
    abi: primaryTokenAbi,
    address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
    functionName: "liquidationUnlockedAt",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return 0
  return Number(data)
}
