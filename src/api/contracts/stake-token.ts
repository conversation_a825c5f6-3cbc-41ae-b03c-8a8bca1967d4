import { formatEtherWithDecimal } from "utils/format"
import estateTokenJson from "./abis/EstateToken.json"
import { useReadContract } from "wagmi"
import { CONTRACT_ADDRESS_ESTATE_TOKEN } from "src/config/env"

export const estateTokenAbi = estateTokenJson.abi

export const useEstateTokenBalance = (
  account: `0x${string}`,
  estateId: string
): { queryKey: readonly unknown[]; value: string } => {
  const { decimals } = useEstateTokenData(estateId)
  const { data, queryKey } = useReadContract({
    abi: estateTokenAbi,
    address: CONTRACT_ADDRESS_ESTATE_TOKEN,
    functionName: "balanceOf",
    args: [account, BigInt(estateId)],
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) {
    return {
      value: "0",
      queryKey,
    }
  }
  return {
    value: formatEtherWithDecimal(String(data), decimals),
    queryKey,
  }
}

export type EstateTokenData = {
  createAt: number
  decimals: number
  expireAt: number
  isDeprecated: boolean
  tokenizationRequestId: bigint
}

export const useEstateTokenData = (estateId: string): EstateTokenData => {
  const { data } = useReadContract({
    abi: estateTokenAbi,
    address: CONTRACT_ADDRESS_ESTATE_TOKEN,
    functionName: "getEstate",
    args: [BigInt(estateId)],
  })
  if (!data) {
    return {
      createAt: 0,
      decimals: 0,
      expireAt: 0,
      isDeprecated: false,
      tokenizationRequestId: 0n,
    }
  }
  return data as EstateTokenData
}
