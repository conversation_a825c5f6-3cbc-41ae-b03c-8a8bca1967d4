import { BigNumber, BigNumberish } from "@ethersproject/bignumber"
import { formatEther } from "@ethersproject/units"
import currency, { Options } from "currency.js"
import Big, { RoundingMode } from "big.js"

export const BASE128 = "340282366920938463463374607431768211456"
export const BASE_CASH = "1000000000000" // 10 ** 12

export const FORMAT_PRECISION = 2

export const toFixed = (amount: string | number, fixed: number = 2) => {
  return new Big(amount).toFixed(fixed)
}

export const formatFixedEther = (
  wei: BigNumberish = 0,
  precision: number = 2
) => {
  return toFixed(formatEther(wei), precision)
}

export const parseCurrency = (
  value: number | string | undefined = 0,
  options: Options = { separator: ",", symbol: "", precision: 2 }
) => {
  const res = currency(value, options).format()
  if (options.precision === 2 && res.endsWith(".00")) {
    return res.slice(0, -3)
  }
  if (options.precision === 2 && res.endsWith("0")) {
    return res.slice(0, -1)
  }
  return res
}

export const zeroPad = (num: number, places: number = 2, pad: string = "0") => {
  return String(num).padStart(places, pad)
}

export const fixedToDecimalFixed = (
  fixed: any = 0,
  precision: number = 10,
  rm: RoundingMode = Big.roundUp,
  base = BASE128
) => {
  return new Big(fixed).div(base).toFixed(precision, rm)
}

export const formatSIPrefix = (num: number = 0, digits: number = 2): string => {
  const lookup = [
    { value: 1, symbol: "" },
    { value: 1e3, symbol: "k" },
    { value: 1e6, symbol: "M" },
    { value: 1e9, symbol: "B" },
    { value: 1e12, symbol: "T" },
  ]
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/
  const item = lookup
    .slice()
    .reverse()
    .find(function (item) {
      return num >= item.value
    })
  return item
    ? (num / item.value).toFixed(digits).replace(rx, "$1") + item.symbol
    : "0"
}

export const mathFloor = (num: number | string, precision: number = 2) => {
  if (typeof num === "string") {
    num = parseFloat(num)
  }
  return Math.floor(num * 10 ** precision) / 10 ** precision
}

export const formatAddress = (address?: string, size: number = 6) => {
  if (!address) {
    return ""
  }
  return `${address.slice(0, size)}...${address.slice(-size + 2)}`
}

export const formatMoney = (value: number | string) => {
  if (typeof value === "string") {
    value = parseFloat(value)
  }
  return value
    .toFixed(2)
    .replace(/\d(?=(\d{3})+\.)/g, "$&,")
    .replace(/\.00$/, "")
}

export const formatNumberToReadableFormat = (num: number): string => {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(1).replace(/\.0$/, "") + "B"
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(1).replace(/\.0$/, "") + "M"
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(1).replace(/\.0$/, "") + "K"
  } else {
    return num.toString()
  }
}

export const formatMoneyWithoutFloat = (value: number | string) => {
  if (typeof value === "string") {
    value = parseInt(value)
  }
  return value
    .toFixed(2)
    .replace(/\d(?=(\d{3})+\.)/g, "$&,")
    .split(".")[0]
}

export const formatCurrency = (
  value: number | string | undefined = 0,
  options: Options = { separator: ",", symbol: "", precision: 2 }
) => {
  const res = currency(value, options).format()
  if (options.precision === 2 && res.endsWith(".00")) {
    return res.slice(0, -3)
  }
  if (options.precision === 2 && res.endsWith(".0")) {
    return res.slice(0, -2)
  }
  return res
}

export const formatEtherWithDecimal = (wei: BigNumberish, decimals: number) => {
  const diff = 18 - decimals
  if (diff >= 0) {
    const modifiedNumber = BigNumber.from(Math.pow(10, diff)).mul(wei)
    return formatEther(modifiedNumber)
  } else {
    // When decimals > 18, we need to divide instead of multiply
    const absDiff = Math.abs(diff)
    const divisor = BigNumber.from(Math.pow(10, absDiff))
    return formatEther(BigNumber.from(wei).div(divisor))
  }
}

export const formatNumericByDecimals = (
  numeric: string,
  decimals: number
): string => {
  const diff = 18 - decimals
  if (diff >= 0) {
    return formatEther(
      (BigInt(numeric) * BigInt(Math.pow(10, diff))).toString()
    ).toString()
  } else {
    // When decimals > 18, we need to divide instead of multiply
    const absDiff = Math.abs(diff)
    return formatEther(
      (BigInt(numeric) / BigInt(Math.pow(10, absDiff))).toString()
    ).toString()
  }
}

export const formatCurrencyByDecimals = (numeric: string, decimals: number) => {
  return formatCurrency(formatNumericByDecimals(numeric, decimals))
}

export const formatMoneyByDecimals = (numeric: string, decimals: number) => {
  return formatMoney(formatNumericByDecimals(numeric, decimals))
}

export const formatNumericByDecimalsDisplay = (
  numeric: string,
  decimals: number
) => {
  return formatMoney(formatNumericByDecimals(numeric, decimals))
}

export const fixedPointMultiply = (A: bigint, B: bigint, BASE: number) => {
  return (A / BigInt(Math.pow(10, BASE))) * B
}

export const formatEtherWithPrecision = (
  value: bigint,
  precision: number = 2
): string => {
  const raw = formatEther(value)
  const [intPart, fracPart = ""] = raw.split(".")
  const fixedFrac = (fracPart + "00").slice(0, precision)
  return `${intPart}.${fixedFrac}`
}

export const formatPercentage = (
  value: number,
  precision: number = 2
): string => {
  const decimalPart = value.toString().split(".")[1]
  if (decimalPart && decimalPart.length >= 3) {
    return value.toFixed(precision)
  }
  return value.toString()
}
