import { LIST_LANDS } from "./routes/RoutesV2"

export type RootStackParamList = {
  // Main navigation
  Tabs: undefined
  CreateNFT: undefined

  // Home stack
  Home: undefined

  // Estate stack
  ListLands: undefined
  EstateTab: {
    screen: typeof LIST_LANDS
    params: {
      tabIndex?: number
    }
  }
  EstateRequestDetail: {
    estateRequestId: string
    userId: string
  }
  EstateDetail: {
    estateId: string
  }

  // Application stack
  ApplicationDetail: {
    applicationId: string
  }

  // Profile stack
  GuestProfile: {
    address?: string
  }
  VerifyAccount: undefined
  EditProfile: undefined

  // Settings stack
  Settings: undefined
  SelectLanguage: {
    selectedLanguage: string
  }
  References: undefined
  Contact: undefined
  Offices: undefined
}

export type TabParamList = {
  HomeTab: undefined
  EstateTab: undefined
  ProfileTab: undefined
  TokensTab: undefined
  SettingsTab: undefined
}
