const en = {
  ntfs: "NTFs",
  erc_1155: "ERC-1155",
  "Binance Smart Chain": "Binance Smart Chain",
  Validating: "Validating",
  Selling: "Selling",
  "Transferring ownership": "Transferring ownership",
  "Failed to transfer ownership": "Failed to transfer ownership",
  "Insufficient sold amount": "Insufficient sold amount",
  Tokenized: "Tokenized",
  "Unknown status": "Unknown status",
  "Tokenization Progress": "Tokenization Progress",
  Validate: "Validate",
  Unverified: "Unverified",
  Verifying: "Verifying",
  Verified: "Verified",
  "Request for verification success": "Request for verification success",
  "Full name": "Full name",
  "Date of birth": "Date of birth",
  "Citizen Identification Number": "Citizen Identification Number",
  "Provide images of your National ID Card":
    "Provide images of your National ID Card",
  "Please provide clear and complete images of both sides of the document.":
    "Please provide clear and complete images of both sides of the document.",
  "Front side": "Front side",
  "Back side": "Back side",
  "Verify account": "Verify account",
  "Verify account success": "Verify account success",
  "Verify account fail": "Verify account fail",
  City: "City",
  District: "District",
  Ward: "Ward",
  "Selling BRIK": "Selling BRIK",
  "auction-TotalDeposit": "Total deposit",
  Treasury: "Treasury",
  "Development Fund": "Development Fund",
  "Backer funds allocation": "Backer funds allocation",
  "Development fund": "Development fund",
  "auction-TimeOut": "Remaining time",
  "common-Auction": "Auction",
  "common-Stake": "Stake",
  "common-Treasury": "Treasury",
  "Request for whitelist": "Request for whitelist",
  "Update contact": "Update contact",
  "Fail to cancel offer": "Fail to cancel offer",
  "Offer has been cancelled": "Offer has been cancelled",
  Cancel: "Cancel",
  "Cancel Offer": "Cancel Offer",
  "Are you sure you want to cancel this offer? This action cannot be undone.":
    "Are you sure you want to cancel this offer? This action cannot be undone.",
  No: "No",
  "Cancelling...": "Cancelling...",
  Yes: "Yes",
  Confirm: "Confirm",
  "Confirm Land Tokenization": "Confirm Land Tokenization",
  "Fail to confirm tokenization": "Fail to confirm tokenization",
  "Successfully tokenized land": "Successfully tokenized land",
  "Commission receiver": "Commission receiver",
  "Confirming...": "Confirming...",
  "Confirm land tokenization": "Confirm land tokenization",
  "Members Development Plan": "Members Development Plan",
  "Real Estate Service Centers": "Real Estate Service Centers",
  "Real Estate Brokers": "Real Estate Brokers",
  Collaborators: "Collaborators",
  "Real estate Department": "Real estate Department",
  "Real estate Saler": "Real estate Saler",
  "Real estate partner": "Real estate partner",
  "What is Briky Land": "What is Briky Land",
  "A revolutionary NFT platform empowering users to own, trade, and create securely on virtual land.":
    "A revolutionary NFT platform empowering users to own, trade, and create securely on virtual land.",
  Tokenomics: "Tokenomics",
  "Road map": "Road map",
  "Audit Report by Verichains": "Audit Report by Verichains",
  "White paper": "White paper",
  Documentation: "Documentation",
  "Future works": "Future works",
  References: "References",
  Contact: "Contact",
  Support: "Support",
  "loader-TitleDeposit": "Auction - Deposit",
  "loader-MessageDeposit": "Depositing...",
  "loader-MessageApprove": "Approving...",
  "Failed to approve": "Failed to approve",
  "Deposit success": "Deposit success",
  "Deposit failed": "Deposit failed",
  "Backer Round auction.": "Backer Round auction.",
  "Seed Round auction.": "Seed Round auction.",
  "Private Sale 1 auction.": "Private Sale 1 auction.",
  "Private Sale 2 auction.": "Private Sale 2 auction.",
  "Public Sale auction.": "Public Sale auction.",
  "Your whitelist request is being validated, please come back later.":
    "Your whitelist request is being validated, please come back later.",
  "Your whitelist request is still in progress, please come back later.":
    "Your whitelist request is still in progress, please come back later.",
  "You must be whitelisted to join our": "You must be whitelisted to join our",
  "You are on the whitelist!": "You are on the whitelist!",
  "You can deposit now.": "You can deposit now.",
  "Please wait for the auction to start depositing.":
    "Please wait for the auction to start depositing.",
  Deposit: "Deposit",
  "You have deposited": "You have deposited",
  "Treasury percentage": "Treasury percentage",
  "Development Fund percentage": "Development Fund percentage",
  "Treasury contribution": "Treasury contribution",
  "Development Fund contribution": "Development Fund contribution",
  "auction-Deposit": "Deposit",
  "loader-TitleWithdraw": "Auction - Withdraw",
  "loader-MessageWithdraw": "Withdrawing...",
  "Withdraw success": "Withdraw success",
  "Withdraw failed": "Withdraw failed",
  "Backer Round": "Backer Round",
  "Please login to deposit.": "Please login to deposit.",
  "Please login to withdraw your BRIK.": "Please login to withdraw your BRIK.",
  "auction-YouHaveDeposited": "You have deposited",
  "auction-WithdrawableAmount": "Withdrawable amount",
  "auction-YouHaveWithdrawnAllYourRewards":
    "Congratulations! You have withdrawn all your BRIK.",
  "auction-Withdraw": "Withdraw",
  "Deposit Tokenization": "Deposit Tokenization",
  "Depositing tokenization...": "Depositing tokenization...",
  "Deposit tokenization success": "Deposit tokenization success",
  "Deposit tokenization failed": "Deposit tokenization failed",
  "Maximum sale": "Maximum sale",
  Sold: "Sold",
  "Ready to deposit": "Ready to deposit",
  "Fail to request for whitelist": "Fail to request for whitelist",
  "Successfully request for whitelist": "Successfully request for whitelist",
  "Referral code": "Referral code",
  "Investment round": "Investment round",
  "Select investment round": "Select investment round",
  "Seed round": "Seed round",
  "Private sale 1": "Private sale 1",
  "Private sale 2": "Private sale 2",
  "Public sale": "Public sale",
  "Investment package": "Investment package",
  "Select investment package": "Select investment package",
  "100$": "100$",
  "1,000$": "1,000$",
  "10,000$": "10,000$",
  Save: "Save",
  "We have received your message. Thank you!":
    "We have received your message. Thank you!",
  "Get in touch": "Get in touch",
  Message: "Message",
  Send: "Send",
  "A new standard in Real Estate Platform":
    "A new standard in Real Estate Platform",
  "Ensuring the sustainable growth and stability of Briky Land":
    "Ensuring the sustainable growth and stability of Briky Land",
  "Key milestones, strategic goals and plans":
    "Key milestones, strategic goals and plans",
  "Seed round is ongoing!": "Seed round is ongoing!",
  "Staking Pool": "Staking Pool",
  "Stake your BRIK to immediately retreive equivalent amount of BRIKI, which will grow everyday":
    "Stake your BRIK to immediately retreive equivalent amount of BRIKI, which will grow everyday",
  "Observe the statistics of tokens issuances and their liquidity":
    "Observe the statistics of tokens issuances and their liquidity",
  Whitepaper: "Whitepaper",
  "Briky Land Whitepaper": "Briky Land Whitepaper",
  "Audited by Verichains": "Audited by Verichains",
  Home: "Home",
  "Briky Land Documentations": "Briky Land Documentations",
  Marketplace: "Marketplace",
  "Investment Deals": "Investment Deals",
  Login: "Login",
  "Open in MetaMask": "Open in MetaMask",
  "Seed Round": "Seed Round",
  "Closing Soon!": "Closing Soon!",
  "Explore now": "Explore now",
  "Private Sale 1": "Private Sale 1",
  "Private Sale 2": "Private Sale 2",
  "Public Sale": "Public Sale",
  "Market Maker": "Market Maker",
  "Core Team": "Core Team",
  "treasury-ExternalTreasury": "External Treasury",
  "Staking Rewards": "Staking Rewards",
  Percentage: "Percentage",
  Allocation: "Allocation",
  Amount: "Amount",
  Total: "Total",
  "Real Estate Tokenization": "Real Estate Tokenization",
  "Native Land": "Native Land",
  "tokens so people can trade these assets partially and globally.":
    " tokens so people can trade these assets partially and globally.",
  Investment: "Investment",
  Deals: "Deals",
  "Become our business partner and together contribute to Briky Land's success":
    "Become our business partner and together contribute to Briky Land's success",
  "Collaborator Package": "Collaborator Package",
  "Become our Collaborator and get return commission":
    "Become our Collaborator and get return commission",
  "Receive BRIK based on the market price at the time of participation":
    "Receive BRIK based on the market price at the time of participation",
  "Invest Now": "Invest Now",
  "Broker Package": "Broker Package",
  "Become a Professional Real Estate Broker":
    "Become a Professional Real Estate Broker",
  "Attain training and certification as a Professional Real Estate Broker":
    "Attain training and certification as a Professional Real Estate Broker",
  "Earn bonuses based on revenue": "Earn bonuses based on revenue",
  "Earn affiliate commissions when NATIVE LANDs (Real Estate NFTs) are traded on the platform":
    "Earn affiliate commissions when NATIVE LANDs (Real Estate NFTs) are traded on the platform",
  "Best Deal": "Best Deal",
  "Partner Package": "Partner Package",
  "Become a Professional Real Estate Investor":
    "Become a Professional Real Estate Investor",
  "Own your Local Real Estate Service Center":
    "Own your Local Real Estate Service Center",
  "No depositors": "No depositors",
  "Selling Progress": "Selling Progress",
  "Property value": "Property value",
  "Initial unit price": "Initial unit price",
  "Real value of property": "Real value of property",
  Minimum: "Minimum",
  NFTs: "NFTs",
  Maximum: "Maximum",
  "Total supply": "Total supply",
  Area: "Area",
  "Token ID": "Token ID",
  "Contract address": "Contract address",
  "Offer list": "Offer list",
  Price: "Price",
  Quantity: "Quantity",
  Diviable: "Diviable",
  From: "From",
  To: "To",
  Proposals: "Proposals",
  Finish: "Finish",
  Time: "Time",
  "From/To": "From/To",
  "No transaction": "No transaction",
  "Select land usage": "Select land usage",
  Residential: "Residential",
  Production: "Production",
  Agricultural: "Agricultural",
  "Non agricultural": "Non agricultural",
  Forest: "Forest",
  "Perennial Crop": "Perennial Crop",
  Industrial: "Industrial",
  "Comming soon": "Comming soon",
  "Withdraw NFT": "Withdraw NFT",
  "Withdrawing NFT(s)...": "Withdrawing NFT(s)...",
  "You have already withdrawn this NFT": "You have already withdrawn this NFT",
  "Withdrawable amount": "Withdrawable amount",
  "loader-TitleLiquidate": "Treasury - Liquidate",
  "loader-MessageLiquidate": "Liquidating...",
  "Liquidate success": "Liquidate success",
  "Liquidate failed": "Liquidate failed",
  "treasury-Liquidation": "Liquidation",
  "treasury-YouWillReceive": "You will receive",
  "treasury-Liquidate": "Liquidate",
  "Unlocked after": "Unlocked after",
  Profile: "Profile",
  "Audit Report": "Audit Report",
  "Log out": "Log out",
  "New Offer": "New Offer",
  "Failed to set approval for all": "Failed to set approval for all",
  "Create offer success": "Create offer success",
  "Create offer failed": "Create offer failed",
  "Fail to create offer": "Fail to create offer",
  "Unit price": "Unit price",
  "Selling amount": "Selling amount",
  NFT: "NFT",
  "Creating New Offer...": "Creating New Offer...",
  "Create New Offer": "Create New Offer",
  "common-day": "day",
  "common-hour": "hour",
  "common-min": "min",
  "common-sec": "sec",
  "auction will start soon.": "auction will start soon.",
  "Please stay tuned! We'll be back with more updates soon.":
    "Please stay tuned! We'll be back with more updates soon.",
  "Staking pool will be opened after BRIK Auction ends.":
    "Staking pool will be opened after BRIK Auction ends.",
  "It's empty": "It's empty",
  Nationality: "Nationality",
  Email: "Email",
  Phone: "Phone",
  "Proposing...": "Proposing...",
  "references-Documentation": "Documentation",
  "references-AddChain": "Add chain",
  "Our journey": "Our journey",
  "Briky Land Platform Announcement": "Briky Land Platform Announcement",
  "Introducing the Briky Land Platform, including our comprehensive Native Land NFT Collection, Native Land NFT Marketplace and the Testnet Beta Platform launch for development and testing purpose.":
    "Introducing the Briky Land Platform, including our comprehensive Native Land NFT Collection, Native Land NFT Marketplace and the Testnet Beta Platform launch for development and testing purpose.",
  "BRIK ICO": "BRIK ICO",
  "Initiation of the BRIK Initial Coin Offering through on-chain auctions with 5 stages of investments":
    "Initiation of the BRIK Initial Coin Offering through on-chain auctions with 5 stages of investments",
  BRIKI: "BRIKI",
  "Introduction of BRIKI, a derivative token designed for staking BRIK.":
    "Introduction of BRIKI, a derivative token designed for staking BRIK.",
  "Native Land P2P Lending": "Native Land P2P Lending",
  "Launch Global P2P features helping people approach low-cost fund. This platform will enable users to leverage their Native Land NFTs as collateral to secure loans, fostering a decentralized lending ecosystem":
    "Launch Global P2P features helping people approach low-cost fund. This platform will enable users to leverage their Native Land NFTs as collateral to secure loans, fostering a decentralized lending ecosystem",
  "Governor Hub": "Governor Hub",
  "Launch of Governor Hub, our governance platform that allows real estate stakeholders to participate in decision-making processes, ensuring a decentralized and community-driven approach.":
    "Launch of Governor Hub, our governance platform that allows real estate stakeholders to participate in decision-making processes, ensuring a decentralized and community-driven approach.",
  "Real Estate Renting Platform": "Real Estate Renting Platform",
  "Introduction of our Real Estate Renting Platform, enabling users to lease tokenized real estate properties, providing a new dimension of utility and income generation within our ecosystem.":
    "Introduction of our Real Estate Renting Platform, enabling users to lease tokenized real estate properties, providing a new dimension of utility and income generation within our ecosystem.",
  "Native Land DEX": "Native Land DEX",
  "Deployment of a Native Land NFT DEX, a transparent and efficient system for listing and trading Native Land NFTs, ensuring optimal market operations and price discovery.":
    "Deployment of a Native Land NFT DEX, a transparent and efficient system for listing and trading Native Land NFTs, ensuring optimal market operations and price discovery.",
  "Unlock BRIK Liquidation": "Unlock BRIK Liquidation",
  "Enabling BRIK token liquidation, providing liquidity options for token holders to seamlessly convert their tokens into other assets.":
    "Enabling BRIK token liquidation, providing liquidity options for token holders to seamlessly convert their tokens into other assets.",
  "loader-TitleStake": "Staking Pool - Stake",
  "loader-MessageStake": "Staking...",
  "stake-TotalStaked": "Total staked",
  "stake-NextDailyReward": "Next daily reward",
  "stake-ReturnedFee": "Returned fee",
  "stake-TotalReward": "Total reward",
  "stake-btnStake": "Stake",
  Max: "Max",
  Buy: "Buy",
  "Take Offer": "Take Offer",
  "Buying NFT...": "Buying NFT...",
  "Buy NFT": "Buy NFT",
  "BRIK distribution": "BRIK distribution",
  "Estimated value": "Estimated value",
  "Estimated price": "Estimated price",
  "Token distribution": "Token distribution",
  "Pre IPO": "Pre IPO",
  Tokenization: "Tokenization",
  "From there, you can trade parts of your asset across the blockchain, globally and transparently with significantly less brokerage cost than traditional markets.":
    "From there, you can trade parts of your asset across the blockchain, globally and transparently with significantly less brokerage cost than traditional markets.",
  "Briky now": "Briky now",
  "treasury-Contribution": "Contribution",
  "treasury-SupplyPercent": "Supply percent",
  "Total BRIK": "Total BRIK",
  "Unlocked amount": "Unlocked amount",
  "treasury-Liquidity": "Liquidity",
  "treasury-Price": "Price",
  "Loading...": "Loading...",
  "loader-TitleUnstake": "Staking Pool - Unstake",
  "loader-MessageUnstake": "Unstaking...",
  "Unstake success": "Unstake success",
  "Unstake failed": "Unstake failed",
  "stake-UnstakingFeeRate": "Unstaking fee rate",
  "stake-DownToZeroPercentage": "(down to 0% in 3 years)",
  "stake-UnstakingFee": "Unstaking fee",
  "stake-ReceivableAmount": "Receivable amount",
  "stake-btnUnstake": "Unstake",
  Account: "Account",
  "Pending Verification": "Pending Verification",
  Disqualified: "Disqualified",
  Voting: "Voting",
  Failed: "Failed",
  Passed: "Passed",
  Confirmed: "Confirmed",
  Pending: "Pending",
  Expired: "Expired",
  "Start Letting": "Start Letting",
  "Cancel Letting": "Cancel Letting",
  "Change Usage": "Change Usage",
  "Extend Expiration": "Extend Expiration",
  Extract: "Extract",
  "Account verification": "Account verification",
  "Unverified account can only interaction with certain tasks. Actions related to selling properties are only available to users who have their information verified":
    "Unverified account can only interaction with certain tasks. Actions related to selling properties are only available to users who have their information verified",
  "Fail to cancel tokenization": "Fail to cancel tokenization",
  "Fail to approve land": "Fail to approve land",
  "Successfully approved land": "Successfully approved land",
  "Successfully rejected land": "Successfully rejected land",
  "Requested on": "Requested on",
  "Certificate of use rights": "Certificate of use rights",
  "Land media": "Land media",
  Reject: "Reject",
  Approve: "Approve",
  "Approve Tokenization Request": "Approve Tokenization Request",
  "Approving...": "Approving...",
  "common-Day": "Day",
  "rewards in": "rewards in",
  "Staking pool": "Staking pool",
  "common-Unstake": "Unstake",
  "treasury-BackerRound": "Backer round",
  "treasury-MarketMaker": "Market Maker",
  "treasury-PrivateSale": "Private sale",
  "treasury-SeedRound": "Seed round",
  "treasury-CoreTeam": "Core Team",
  "treasury-Esop": "ESOP",
  "Total BRIKI": "Total BRIKI",
  "(Staked BRIK)": "(Staked BRIK)",
  APY: "APY",
  "After Auction ends": "After Auction ends",
  "Daily staking rewards": "Daily staking rewards",
  Description: "Description",
  Address: "Address",
  Traits: "Traits",
  Details: "Details",
  "Request ID": "Request ID",
  "Token standard": "Token standard",
  Chain: "Chain",
  Category: "Category",
  Depositors: "Depositors",
  "Please input the min selling amount": "Please input the min selling amount",
  "Please input a valid number": "Please input a valid number",
  "Min selling amount must be greater than 0":
    "Min selling amount must be greater than 0",
  "Please input the max selling amount": "Please input the max selling amount",
  "Max selling amount must be greater than 0":
    "Max selling amount must be greater than 0",
  "Min selling amount must be less than or equal to max selling amount":
    "Min selling amount must be less than or equal to max selling amount",
  "Name must be at least 20 characters long":
    "Name must be at least 20 characters long",
  "Name must be at most 100 characters long":
    "Name must be at most 100 characters long",
  "Serial number is not in the correct format, ex: AB123456":
    "Serial number is not in the correct format, ex: AB123456",
  "Address must be at least 20 characters long":
    "Address must be at least 20 characters long",
  "Address must be at most 255 characters long":
    "Address must be at most 255 characters long",
  "Please select a city/province": "Please select a city/province",
  "Please select a district": "Please select a district",
  "Please select a ward": "Please select a ward",
  "Please input the area of the property":
    "Please input the area of the property",
  "Description must be at least 500 characters long":
    "Description must be at least 500 characters long",
  "Description must be at most 10000 characters long":
    "Description must be at most 10000 characters long",
  "Please input the unit price": "Please input the unit price",
  "Unit price must be greater than 0": "Unit price must be greater than 0",
  "Please input the total supply": "Please input the total supply",
  "Total supply must be at least 10000": "Total supply must be at least 10000",
  "Please upload at least 2 images of land use rights":
    "Please upload at least 2 images of land use rights",
  "Please upload at least 4 images of the property":
    "Please upload at least 4 images of the property",
  "Please input property expiry date": "Please input property expiry date",
  "The expiry date must be at least 1 year from now":
    "The expiry date must be at least 1 year from now",
  "Please input the public sale duration":
    "Please input the public sale duration",
  "Invalid duration": "Invalid duration",
  "Request for tokenization failed": "Request for tokenization failed",
  "Request for tokenization success": "Request for tokenization success",
  minute: "minute",
  hour: "hour",
  day: "day",
  week: "week",
  month: "month",
  "Request for Real Estate Tokenization":
    "Request for Real Estate Tokenization",
  "You need to verify your account to start creating NFT.":
    "You need to verify your account to start creating NFT.",
  "Click here to verify your account.": "Click here to verify your account.",
  "Name of the property": "Name of the property",
  "Example City center apartment": "Example City center apartment",
  "Serial Number": "Serial Number",
  "Example AB123456": "Example AB123456",
  Location: "Location",
  "Example No. 1, Nguyen Hue Street": "Example No. 1, Nguyen Hue Street",
  "Land usage": "Land usage",
  "Non-agricultural": "Non-agricultural",
  "Date of expiry": "Date of expiry",
  Denominations: "Denominations",
  "Price of each ERC1155 token": "Price of each ERC1155 token",
  "ERC1155 total supply": "ERC1155 total supply",
  "The total sold amount cannot exceed the maximum selling amount. If the minimum selling amount is not met by the end of the selling period, the tokenization process will fail.":
    "The total sold amount cannot exceed the maximum selling amount. If the minimum selling amount is not met by the end of the selling period, the tokenization process will fail.",
  "Minimum selling amount": "Minimum selling amount",
  "Maximum selling amount": "Maximum selling amount",
  "Public sale duration": "Public sale duration",
  "Land use rights images": "Land use rights images",
  "Please upload clear photos of both sides of the document":
    "Please upload clear photos of both sides of the document",
  "Images of the property": "Images of the property",
  "Property description": "Property description",
  "Enter the description for the property":
    "Enter the description for the property",
  "Request for tokenization": "Request for tokenization",
  "Progressing...": "Progressing...",
  "Request for Tokenization": "Request for Tokenization",
  "Insufficient Sold Amount": "Insufficient Sold Amount",
  "Transferring ownership Failed": "Transferring ownership Failed",
  Timestamp: "Timestamp:",
  "Token Price": "Token Price",
  "Total Supply": "Total Supply",
  "Native Land NFT Marketplace": "Native Land NFT Marketplace",
  "List Your Real Estate": "List Your Real Estate",
  Filters: "Filters",
  All: "All",
  Status: "Status",
  "Sort By": "Sort By:",
  Descending: "Descending",
  "Posted at": "Posted at",
  "Verify Account": "Verify Account",
  Wallet: "Wallet",
  "Tokenizing Assets": "Tokenizing Assets",
  "Personal Information": "Personal Information",
  Holders: "Holders",
  Offers: "Offers",
  Loans: "Loans",
  Loan: "Loan",
  Duration: "Duration",
  "Mortgage amount": "Mortgage amount",
  Principal: "Principal",
  Repayment: "Repayment",
  Mortgage: "Mortgage",
  "Take Photo": "Take Photo",
  "Choose from Library": "Choose from Library",
  "Mortgage Hub": "Mortgage Hub",
  "Real Estate Tokenization Requests": "Real Estate Tokenization Requests",
  "Mortgage Hub Loans": "Mortgage Hub Loans",
  "Mortgage Native Land": "Mortgage Native Land",
  Borrow: "Borrow",
  Repaid: "Repaid",
  Foreclose: "Foreclose",
  Foreclosed: "Foreclosed",
  Cancelled: "Cancelled",
  Funded: "Funded",
  Open: "Open",
  Lend: "Lend",
  "Earn permanent affiliate commissions when NATIVE LANDs (Real Estate NFTs) are traded on the platform":
    "Earn permanent affiliate commissions when NATIVE LANDs (Real Estate NFTs) are traded on the platform",
  Repay: "Repay",
  "No loans": "No loans",
  "Please input the mortgage duration": "Please input the mortgage duration",
  "Borrow success": "Borrow success",
  "Borrow failed": "Borrow failed",
  "Fail to borrow": "Fail to borrow",
  "Creating Borrow Request...": "Creating Borrow Request...",
  dividable: "dividable",
  "Seller address": "Seller address",
  "Price per token": "Price per token",
  "Seed Round auction will start soon.Please stay tuned! We'll be back with more updates soon.":
    "Seed Round auction will start soon.Please stay tuned! We'll be back with more updates soon.",
  "Referral Code": "Referral Code",
  "String must contain at least 8 character(s)":
    "String must contain at least 8 character(s)",
  Country: "Country",
  Accept: "Accept",
  Reset: "Reset",
  Owner: "Owner",
  NFTOwn: "NFTOwn",
  "Pending Approval": "Pending Approval",
  Digitized: "Digitized",
  "Transfer of ownership": "Transfer of ownership",
  VIETNAM: "VIETNAM",
  ARAP: "ARAP",
  "Oops!": "Oops!",
  "It's Empty": "It's Empty",
  // TODO wait string final
  "Estate Requests": "Estate Requests",
  "My Applications": "My Applications",
  "Thumnail of property": "Thumnail of property",
  "Please upload thumbnail": "Please upload thumbnail",
  "Created at": "Created at:",
  "Withdraw NFT fail": "Withdraw NFT fail",
  "Withdraw NFT success": "Withdraw NFT success",
  sqm: "sqm",
  sqft: "sqft",
  Overdue: "Overdue",
  Supplied: "Supplied",
  Done: "Done",
  "Public selling": "Public selling",
  Recalled: "Recalled",
  NotExpired: "NotExpired",
  "My wallet": "My wallet",
  "My Estate": "My Estate",
  Refunded: "Refunded",
  "Waiting handle": "Waiting handle",
  "Not expire": "Not expire",
  "Remaining open time": "Remaining open time",
  "Time up": "Time up",
  "Selling NFT number": "Selling NFT number",
  "Matched NFT number": "Matched NFT number",
  "Max NFT number": "Max NFT number",
  "Min NFT number": "Min NFT number",
  "Deposited NFT number": "NFT number deposited",
  "Due date": "Due date",
  "Initial price": "Initial price",
  "Cancel success": "Cancel success",
  "Cancel failed": "Cancel failed",
  "Lend success": "Lend success",
  "Lend failed": "Lend failed",
  "Repay success": "Repay success",
  "Repay failed": "Repay failed",
  "Foreclose success": "Foreclose success",
  "Foreclose failed": "Foreclose failed",
  Processing: "Processing",
  "Aiming to sustain growth and stability through “fair distribution”.":
    "Aiming to sustain growth and stability through “fair distribution”.",
  "We foster long-term value and active user participation.":
    "We foster long-term value and active user participation.",
  "Key milestones.": "Key milestones.",
  "Strategic goals.": "Strategic goals.",
  "Planning to grow the user base.": "Planning to grow the user base.",
  "Integrating our advanced features.": "Integrating our advanced features.",
  "Traditional market": "Traditional market",
  "High costs": "High costs",
  "Limited access": "Limited access",
  "Complex transactions in real estate.":
    "Complex transactions in real estate.",
  "Briky Land's NFT System": "Briky Land's NFT System",
  "“NFT Representation” Verifies ownership and authenticity.":
    "“NFT Representation” Verifies ownership and authenticity.",
  "“Fractional Investment” Enables accessible property ownership.":
    "“Fractional Investment” Enables accessible property ownership.",
  "“Secure Transactions” Ensured by blockchain smart contracts.":
    "“Secure Transactions” Ensured by blockchain smart contracts.",
  "User Benefits:": "User Benefits:",
  "Lower investment barriers": "Lower investment barriers",
  "Increased liquidity": "Increased liquidity",
  "Efficient and reliable process": "Efficient and reliable process",
  "ERC-Driven Evolution": "ERC-Driven Evolution",
  "Boosting digital assets with the power of “ERC-1155” and “ERC-20”":
    "Boosting digital assets with the power of “ERC-1155” and “ERC-20”",
  "Enabling seamless trading, fractional ownership, low fees, and advanced security.":
    "Enabling seamless trading, fractional ownership, low fees, and advanced security.",
  "Transformed by AI and Blockchain": "Transformed by AI and Blockchain",
  "Enhancing “smarter decisions” and “secure transactions”":
    "Enhancing “smarter decisions” and “secure transactions”",
  "Connecting buyers and sellers “in real time” to boost transparency and liquidity.":
    "Connecting buyers and sellers “in real time” to boost transparency and liquidity.",
  "“BRIKI” TOKEN": "“BRIKI” TOKEN",
  "Primary Token: Used for buying and selling real estate, “starting from just $100”.":
    "Primary Token: Used for buying and selling real estate, “starting from just $100”.",
  "Multi-Use: Covers platform fees seamlessly.":
    "Multi-Use: Covers platform fees seamlessly.",
  "Real estate type": "Real estate type",
  "Estate have sell NFT order": "Estate have sell NFT order",
  "Estate have loan": "Estate have loan",
  Apply: "Apply",
  "Send from": "Send from",
  "Transaction code": "Transaction code:",
  "Numer of NFTs": "Numer of NFTs",
  ago: "ago",
  "Deposit price": "Deposit price",
  USDT: "USDT",
  Unit: "Unit",
  Information: "Information",
  VND: "VND",
  "Select area": "Select area",
  "Requires full and clear photos of both sides of documents":
    "Requires full and clear photos of both sides of documents",
  "Address copied": "Address copied",
  "Update avatar success": "Update avatar success",
  "Update avatar fail": "Update avatar fail",
  Sunday: "Sunday",
  Monday: "Monday",
  Tuesday: "Tuesday",
  Wednesday: "Wednesday",
  Thursday: "Thursday",
  Friday: "Friday",
  Saturday: "Saturday",
  "Broker information": "Broker information",
  "Wallet address": "Wallet address:",
  "Enter wallet address": "Enter wallet address",
  "Please input a valid mortgage amount greater than 0":
    "Please input a valid mortgage amount greater than 0",
  "Please input a valid principal amount greater than 0":
    "Please input a valid principal amount greater than 0",
  "Please input a valid repayment amount greater than 0":
    "Please input a valid repayment amount greater than 0",
  "Please input a valid unit price greater than 0":
    "Please input a valid unit price greater than 0",
  "Please input a valid selling amount greater than 0":
    "Please input a valid selling amount greater than 0",
  "Number of NFTs owned": "Number of NFTs owned",
  Deposits: "Deposits",
  // New translations for SlideBar
  BRIK: "BRIK",
  HomePath: "Home",
  Estate: "Estate",
  MarketPlace: "Marketplace",
  MortgageHub: "Mortgage Hub",
  Auction: "Auction",
  StakingPool: "Staking Pool",
  Setting: "Setting",
  Settings: "Settings",
  // New translations for Navigator
  ListLands: "List Lands",
  LandDetail: "Land Detail",
  EstateRequestDetail: "Estate Request Detail",
  EstateDetail: "Estate Detail",
  CreateNFT: "Create NFT",
  MyProfile: "My Profile",
  VerifyAccount: "Verify Account",
  // New translations for AuthContext
  "No provider found": "No provider found",
  "Wallet disconnect error:": "Wallet disconnect error:",
  "Authentication failed:": "Authentication failed:",
  "Login failed. Please try again.": "Login failed. Please try again.",
  "Session Expired": "Session Expired",
  "Please login again": "Please login again",
  // New translations for NavigationBar
  "Connect wallet": "Connect wallet",
  "Sign message": "Sign message",
  Continue: "Continue",
  "Connect a wallet": "Connect a wallet",
  "Marketplace Offers": "Marketplace Offers",
  Language: "Language",
  Informations: "Informations",
  "Select Language": "Select Language",
  "Vertichain Security Report": "Vertichain Security Report",
  Logout: "Logout",
  Value: "Value",
  "Transaction Hash": "Transaction Hash",
  "Valid photo.": "Valid photo.",
  "Front of ID card:": "Front of ID card:",
  "Take a photo of the front side of your identification card.":
    "Take a photo of the front side of your identification card.",
  "Back of ID card:": "Back of ID card:",
  "Take a photo of the back side of your identification card.":
    "Take a photo of the back side of your identification card.",
  "Submit Photo": "Submit Photo",
  "Estate detail": "Estate detail",
  "Data will be updated in few seconds": "Data will be updated in few seconds",
  Date: "Date",
  dayUnit: "D",
  hourUnit: "H",
  minuteUnit: "M",
  secondUnit: "S",
  seconds: "seconds",
  minutes: "minutes",
  hours: "hours",
  days: "days",
  years: "years",
  "Starting price": "Starting price",
  "Maximum NFTs deposit": "Maximum NFTs deposit",
  "Minimum NFTs to deposit": "Minimum NFTs to deposit",
  "Deposited NFTs": "Deposited NFTs",
  "Awaiting Approval": "Awaiting Approval",
  Rejected: "Rejected",
  "My Estates": "My Estates",
  "Tokenization Requests": "Tokenization Requests",
  "Asking price of a NFT": "Asking price of a NFT",
  "Total NFTs": "Total NFTs",
  "NFTs sold": "NFTs sold",
  "Partial match": "Partial match",
  "Expired at": "Expired at",
  "NFT Mortgage": "NFT Mortgage",
  "Tokens to borrow": "Tokens to borrow",
  "Tokens to repay": "Tokens to repay",
  Arrangement: "Arrangement",
  "Your NFTs": "Your NFTs",
  Alias: "Alias",
  "Upload avatar": "Upload avatar",
  "Credential Type": "Credential Type",
  "City/Province": "City/Province",
  Street: "Street",
  "Road Access": "Road Access",
  Bedrooms: "Bedrooms",
  "Built SQM": "Built SQM",
  Rooms: "Rooms",
  "Living Rooms": "Living Rooms",
  Landscape: "Landscape",
  Floors: "Floors",
  Kitchens: "Kitchens",
  Bathrooms: "Bathrooms",
  Other: "Other",
  Nation: "Nation",
  "Tokenized successfully from": "Tokenized successfully from",
  "Unexpected error. Please try again.": "Unexpected error. Please try again.",
  "Please switch to the correct network":
    "Please switch to the correct network",
  Unknown: "Chưa rõ",
  "Please input the decimals": "Please input the decimals",
  "Number of NFTs": "Number of NFTs",
  "Transaction hash": "Transaction hash",
  "Your number of NFTs": "Your number of NFTs",
  "Market Price": "Market Price",
  "Full name must be at least 2 characters":
    "Full name must be at least 2 characters",
  "Full name must be at most 100 characters":
    "Full name must be at most 100 characters",
  "Please select Date of birth": "Please select Date of birth",
  "Citizen ID must be at least 9 characters":
    "Citizen ID must be at least 9 characters",
  "Citizen ID must be at most 12 characters":
    "Citizen ID must be at most 12 characters",
  "Please input alias": "Please input alias",
  "Invalid email": "Invalid email",
  "Phone number must be at least 10 digits":
    "Phone number must be at least 10 digits",
  "Phone number must not exceed 12 digits":
    "Phone number must not exceed 12 digits",
  "Buy NFT success": "Buy NFT success",
  "Buy NFT failed": "Buy NFT failed",
  "You must be at least 18 years old": "You must be at least 18 years old",
  "Please input message": "Please input message",
  "Message must not exceed 512 characters":
    "Message must not exceed 512 characters",
  Overview: "Overview",
  "Tokenomics allocation": "Tokenomics allocation",
  "On going": "On Going",
  "Slow withdraw": "Slow withdraw",
  Withdraw: "Withdraw",
  "Has withdrawn": "Has withdrawn",
  "BRIK unlock": "BRIK unlock",
  "Your investment": "Your investment",
  "Enter the amount you want to withdraw slowly":
    "Enter the amount you want to withdraw slowly",
  "Total amount": "Total amount",
  "BRIK sold": "BRIK sold",
  "Request sent failed": "Request sent failed",
  "Round investors list": "List of {{round}} investors",
  Office: "Office",
  Representative: "Representative",
  "Wallet Address": "Wallet Address",
  "BRIK amount": "BRIK amount",
  "No transactions": "No transactions",
  Activities: "Activities",
  "No activities": "No activities",
  Sale: "Sale",
  Transfer: "Transfer",
  Retrieve: "Retrieve",
  "My Wallet": "My Wallet",
  Legal: "Legal",
  "Review legal document": "Review legal document",
  "legal document": "legal document",
  "N/A": "N/A",
  // Error messages
  "Could not bind Multipart Form": "Could not bind Multipart Form",
  "Invalid request": "Invalid request",
  "Invalid nonce": "Invalid nonce",
  "Invalid signature": "Invalid signature",
  "Invalid file": "Invalid file",
  "Could not parse Multipart Form": "Could not parse Multipart Form",
  "Could not get file from Multipart Form":
    "Could not get file from Multipart Form",
  "Could not get Multipart Form data": "Could not get Multipart Form data",
  "At least 2 Credential photos required":
    "At least 2 Credential photos required",
  "At least 4 Estate photos required": "At least 4 Estate photos required",
  "Unsupported zone": "Unsupported zone",
  "Could not bind Pagination": "Could not bind Pagination",
  "Invalid path parameter": "Invalid path parameter",
  "Wrong broker email verification code":
    "Wrong broker email verification code",
  "Could not bind Filter Query": "Could not bind Filter Query",
  "Invalid query parameter": "Invalid query parameter",
  "Unavailable currency": "Unavailable currency",
  "Invalid unit price": "Invalid unit price",
  "Broker email has already been verified":
    "Broker email has already been verified",
  "Broker email verification code expired":
    "Broker email verification code expired",
  "Invalid authorization header": "Invalid authorization header",
  "Invalid JWT": "Invalid JWT",
  "Malformed JWT": "Malformed JWT",
  "Wrong password": "Wrong password",
  "Access denied": "Access denied",
  "User has already been verified": "User has already been verified",
  "Nonce does not exist": "Nonce does not exist",
  "Currency not found": "Currency not found",
  "Broker wallet not found": "Broker wallet not found",
  "Estate token metadata not found": "Estate token metadata not found",
  "Tokenization application not found": "Tokenization application not found",
  "Broker email not found": "Broker email not found",
  "Internal error": "Internal error",
  "Could not generate nonce": "Could not generate nonce",
  "Could not set nonce to Redis": "Could not set nonce to Redis",
  "Could not get nonce from Redis": "Could not get nonce from Redis",
  "Could not generate JWT string": "Could not generate JWT string",
  "Could not get my address from Gin Context":
    "Could not get my address from Gin Context",
  "My address from Gin Context is invalid":
    "My address from Gin Context is invalid",
  "Could not get or create user": "Could not get or create user",
  "Could not create estate token metadata":
    "Could not create estate token metadata",
  "Could not create tokenization application":
    "Could not create tokenization application",
  "Could not upload tokenization application files":
    "Could not upload tokenization application files",
  "Could not create tokenization application files record":
    "Could not create tokenization application files record",
  "Database error": "Database error",
  "Could not get file from S3": "Could not get file from S3",
  "Could not pin file to Pinata": "Could not pin file to Pinata",
  "Could not marshal EstateTokenMetadata JSON":
    "Could not marshal EstateTokenMetadata JSON",
  "Could not encrypt broker password": "Could not encrypt broker password",
  "Could not send email via Sendgrid": "Could not send email via Sendgrid",
  "Email verification code expiredAt is null":
    "Email verification code expiredAt is null",
  "Could not get broker ID from Gin Context":
    "Could not get broker ID from Gin Context",
  "Invalid broker ID from Gin Context": "Invalid broker ID from Gin Context",
  "Could not open national ID card image file":
    "Could not open national ID card image file",
  "Could not upload national ID card image file":
    "Could not upload national ID card image file",
  "Could not create file record": "Could not create file record",
  "Could not override user KYC data": "Could not override user KYC data",
  "Could not open avatar image file": "Could not open avatar image file",
  "Could not upload avatar image file": "Could not upload avatar image file",
  "Could not override user data": "Could not override user data",
  "Could not find estate": "Could not find estate",
  "Could not create appraisal document files":
    "Could not create appraisal document files",
  "Could not create land registry document files":
    "Could not create land registry document files",
  "An error has occurred!": "An error has occurred!",
  "An error has occurred": "An error has occurred",
  Offices: "Offices",
  "Viet Nam Office": "Viet Nam Office",
  "Singapore Office": "Singapore Office",
  "Australia Office": "Australia Office",
  "Dubai Office": "Dubai Office",
  "Briky Land Real Estate Trading Floor Joint Stock Company":
    "Briky Land Real Estate Trading Floor Joint Stock Company",
  "Brikyland Holding Pte.Ltd": "Brikyland Holding Pte.Ltd",
  "Brikyland Australia Pty.Ltd": "Brikyland Australia Pty.Ltd",
  "Briky Land Virtual Assets Management Investment Services L.L.C":
    "Briky Land Virtual Assets Management Investment Services L.L.C",
  "Tax registration office": "Tax registration office",
  "No. 1A, Lane 57 Nguyen Khanh Toan, Dich Vong Ward, Cau Giay District, Hanoi City":
    "No. 1A, Lane 57 Nguyen Khanh Toan, Dich Vong Ward, Cau Giay District, Hanoi City",
  "Hanoi Office 1 - real estate floor headquarters":
    "Hanoi Office 1 - real estate floor headquarters",
  "Hanoi Office 2": "Hanoi Office 2",
  "Building N01-T2, Diplomatic Corps Urban Area, Hoang Minh Thao Street, Xuan Tao Ward, Bac Tu Liem District, Hanoi.":
    "Building N01-T2, Diplomatic Corps Urban Area, Hoang Minh Thao Street, Xuan Tao Ward, Bac Tu Liem District, Hanoi.",
  "5th Floor, SME Royal Building, To Hieu Street, Quang Trung Ward, Ha Dong District, Hanoi.":
    "5th Floor, SME Royal Building, To Hieu Street, Quang Trung Ward, Ha Dong District, Hanoi.",
  "Ho Chi Minh Office": "Ho Chi Minh Office",
  "178-180 Le Hong Phong Street, Ward 3, District 5, Ho Chi Minh City.":
    "178-180 Le Hong Phong Street, Ward 3, District 5, Ho Chi Minh City.",
  "114 Lavender Street, #11-83 CT HUB 2, Singapore.":
    "114 Lavender Street, #11-83 CT HUB 2, Singapore.",
  "Suit 886, 100 George Street, Parramatta NSW 2150 Australia.":
    "Suit 886, 100 George Street, Parramatta NSW 2150 Australia.",
  "Delete Account": "Delete Account",
  "You need to grant access to the photo library":
    "You need to grant access to the photo library",
  "You need to grant camera access": "You need to grant camera access",
  "Is Infinite": "Is Infinite",
  Decimals: "Decimals",
  "Wallet address not found": "Wallet address not found",
  "This wallet address is the same as your wallet address":
    "This wallet address is the same as your wallet address",
  "Invalid wallet address": "Invalid wallet address",
  "Please input value in range from min to max":
    "Please input value in range from {{min}} to {{max}}",
  "Max selling amount must be less than or equal to total supply":
    "Max selling amount must be less than or equal to total supply",
  "Request permission": "Request permission",
  "Open settings": "Open settings",
  "Owner information": "Owner information",
  "Amount must be greater than 0": "Amount must be greater than 0",
  "Please input wallet address": "Please input wallet address",
  "Amount must not exceed your balance": "Amount must not exceed your balance",
  "Please wait for balance to load": "Please wait for balance to load",
  "The next staking time will be after": "The next staking time will be after",
  Stake: "Stake",
  Unstake: "Unstake",
  "sec(s)": "sec(s)",
  "min(s)": "min(s)",
  "hour(s)": "hour(s)",
  "day(s)": "day(s)",
  "just now": "just now",
  second: "second",
  year: "year",
  "invalid time": "invalid time",
  "future time": "future time",
  Brik: "Brik",
  "Not empty": "Not empty",
  "Mortgage Open": "Open",
  "Mortgage Cancelled": "Cancelled",
  "Mortgage Lent": "Lent",
  "Mortgage Overdue": "Over due",
  "Mortgage Foreclosed": "Foreclosed",
  "Mortgage Repaid": "Repaid",
  "BRIK sent": "BRIK sent",
  "Next daily interest": "Next daily interest",
  "Savings withdrawal fee": "Savings withdrawal fee",
  "Total profit": "Total profit",
  apy: "apy",
  "After Public Sale": "After Public Sale",
  "Stake affter": "Stake after {{time}}",
  "Enter the deposit amount": "Enter the deposit amount",
  Unstaking: "Unstaking",
  "Unstaking fee rate": "Unstaking fee rate",
  "Unstaking fee": "Unstaking fee",
  "Receivable amount": "Receivable amount",
  In: "In",
  "Hot Deals": "Hot Deals",
  "24 Hours": "24 Hours",
  "7 Days": "7 Days",
  "14 Days": "14 Days",
  "30 Days": "30 Days",
  "6 Months": "6 Months",
  Raised: "Raised",
  Explore: "Explore",
  "Sale Live": "Sale Live",
  "Featured Estates": "Featured Estates",
  Upcoming: "Upcoming",
  "Recent Offers": "Recent Offers",
  Briky101: "Briky101",
  "Administrative Process": "Administrative Process",
  "Real Estate Documents": "Real Estate Documents",
  "Company Documents": "Company Documents",
  "Property Owner Documents": "Property Owner Documents",
  "Legal Evaluation": "Legal Evaluation",
  Valuation: "Valuation",
  "Tax Filing": "Tax Filing",
  "Transfer Completion": "Transfer Completion",
  "Digitization Process": "Digitization Process",
  Issuer: "Issuer",
  Detail: "Detail",
  "Certificate of Real Estate Ownership/Use Rights":
    "Certificate of Real Estate Ownership/Use Rights",
  "Company Identity Verification": "Company Identity Verification",
  "Legal Representative Identity Verification in Vietnam":
    "Legal Representative Identity Verification in Vietnam",
  "Verification of Relationship with Parent Company":
    "Verification of Relationship with Parent Company",
  "Property Owner Identity Verification":
    "Property Owner Identity Verification",
  "Property Transfer from Owner to Company":
    "Property Transfer from Owner to Company",
  "Property Sealing": "Property Sealing",
  "Legal Due Diligence of Property": "Legal Due Diligence of Property",
  "Marital Status of Property Owner": "Marital Status of Property Owner",
  "Property Valuation": "Property Valuation",
  "Personal Income Tax": "Personal Income Tax",
  "Value-Added Tax (VAT)": "Value-Added Tax (VAT)",
  "Registration Fee": "Registration Fee",
  "Resource Tax": "Resource Tax",
  "Updated Certificate of Land Use Rights":
    "Updated Certificate of Land Use Rights",
  "Enable digitization, token minting, payments":
    "Enable digitization, token minting, payments",
  "Transfer of real estate from mortgagee to company":
    "Transfer of real estate from mortgagee to company",
  "Mortgage release": "Mortgage",
  "No legal requirement": "No legal requirement",
  "Legal documents": "Legal documents",
  "Remaining opening time": "Remaining opening time",
  d: "d",
  h: "h",
  m: "m",
  s: "s",
  "Cannot transfer to your own address": "Cannot transfer to your own address",
  "Please enter seller's wallet address":
    "Please enter seller's wallet address",
  Available: "Available",
  tokens: "tokens",
  "at price": "at",
  per: "per",
  "List in": "List in",
  By: "By",
  "Real Estate": "Real Estate",
  "Price per NFT": "Price per NFT",
  "Total Price": "Total Price",
  Divisible: "Divisible",
  "Created by": "Created by",
  "Buy now": "Buy now",
  "Results matching you": "{{results}} Results matching you",
  Recent: "Recent",
  "Selling amount cannot exceed your NFT balance":
    "Selling amount cannot exceed your NFT balance",
  "Mortgage amount cannot exceed your NFT balance":
    "Mortgage amount cannot exceed your NFT balance",
  Onchain: "Onchain",
  Network: "Network",
  "No traits": "No traits",
  "Show all traits": "Show all {{traitsSize}} traits",
  "Show less": "Show less",
  Acreage: "Acreage",
  "Posted on": "Posted on",
  "Land authority": "Land authority",
  Attachment: "Attachment",
  stage: "Stage",
  "No description": "No description",
  "Min. Buy": "Min. Buy",
  "Max. Buy": "Max. Buy",
  "Raise Progress": "Raise Progress",
  Ended: "Ended",
  "Successfully tokenized at": "Successfully tokenized at",
  "Enter wallet destination": "Enter wallet destination",
  "Enter NFT amount": "Enter NFT amount",
  "Estate Value": "Estate Value",
  "Connect Wallet": "Connect Wallet",
  "Sale Ended": "Sale Ended",
  "Cant Deposit": "Cant Deposit",
  "Cant withdraw": "Cant withdraw",
  deposit: "deposit",
  "My Deposited": "My Deposited",
  "Since the transfer period has expired, you will be allowed to withdraw your":
    "Since the transfer period has expired, you will be allowed to withdraw your",
  "Verify my account": "Verify my account",
  Submit: "Submit",
  "Display name": "Display name",
  "Edit profile success": "Edit profile success",
  "Edit profile fail": "Edit profile fail",
  "Verify now": "Verify now",
  "Request for real estate tokenization":
    "Request for real estate tokenization",
  Warning: "Warning",
  "Property Details": "Property Details",
  "Token Economic": "Token Economic",
  "Property Images": "Property Images",
  "User Information": "User Information",
  "Submit Tokenization Request": "Submit Tokenization Request",
  "Not Verified": "Not Verified",
  "Seller Information": "Seller Information",
  "Broker Information": "Broker Information",
  "Input wallet address of the seller to search":
    "Input wallet address of the seller to search",
  "Input wallet address of the broker to search":
    "Input wallet address of the broker to search",
  "Public Sale Duration": "Public Sale Duration",
  "Land use rights images (minimum 2 images)":
    "Land use rights images (minimum 2 images)",
  "Thumbnail of the property (1 image)": "Thumbnail of the property (1 image)",
  "Images of the property (minimum 4 images)":
    "Images of the property (minimum 4 images)",
  "Upload file": "Upload file",
  "Your account has been verified. You can create a tokenization request now":
    "Your account has been verified. You can create a tokenization request now",
  "Name of the seller": "Name of the seller",
  "Name of the broker": "Name of the broker",
  "Nationality ID": "Nationality ID",
  "Front of ID Card": "Front of ID Card",
  "Back of ID Card": "Back of ID Card",
  "Edit profile": "Edit profile",
  Avatar: "Avatar",
  "Delete account": "Delete account",
  "Withdraw NFT(s) success. Data will be updated in few seconds":
    "Withdraw NFT(s) success. Data will be updated in few seconds",
  "Withdraw NFT(s) failed": "Withdraw NFT(s) failed",
  withdrawDepositSuccessMessage:
    "Withdraw {{token}} success. Data will be updated in few seconds.",
  withdrawDepositFailed: "Withdraw {{token}} failed.",
  selectedRowsPlaceholder: "{{count}} of {{total}} selected",
  pageInfoPlaceholder: "Page {{current}} of {{total}}",
  "Link copied to clipboard": "Link copied to clipboard",
  "Min selling amount must be less than or equal to total supply":
    "Min selling amount must be less than or equal to total supply",
  "Max selling amount must be greater than or equal to min selling amount":
    "Max selling amount must be greater than or equal to min selling amount",
  "Briky Land Tokens": "Briky Land Tokens",
  "View less": "View less",
  "View more": "View more",
  "Live Now": "Live Now",
  "Coming Soon": "Coming Soon",
  "Sale Completed": "Sale Completed",
  "Tokens for Sale": "Tokens for Sale",
  "Percentage Supply": "Percentage Supply",
  "Total Fund Raised": "Total Fund Raised",
  Unlocked: "Unlocked",
  "Total Contributor": "Total Contributor",
  "Round Overview": "{{round}} Overview ",
  "Percent Holding": "Percent Holding",
  "Total Allocation": "Total Allocation",
  "Raise Process": "Raise Process",
  "Vesting time": "Vesting time",
  "Vesting percentage": "Vesting percentage",
  "Vesting Information": "Vesting Information",
  Cliff: "Cliff",
  months: "months",
  "%": "%",
  "-": "-",
  "100%": "100%",
  "20B": "20B",
  AB123456: "AB123456",
  "Account Verified": "Account Verified",
  "Account Verifying": "Account Verifying",
  Activity: "Activity",
  "Add the description for the property":
    "Add the description for the property",
  "After auction ends": "After auction ends",
  Allocated: "Allocated",
  Application: "Application",
  "Approve failed": "Approve failed",
  "BNB Chain": "BNB Chain",
  "BNB Chain Testnet": "BNB Chain Testnet",
  BRIKSTAKE: "BRIKSTAKE",
  Balance: "Balance",
  "Binance Smart Chain Testnet": "Binance Smart Chain Testnet",
  BrikStake: "BrikStake",
  "Buy NFTs": "Buy NFTs",
  "Buy Now": "Buy Now",
  Canceled: "Canceled",
  "Core team": "Core team",
  "Create Offer": "Create Offer",
  "Credential ID": "Credential ID",
  "Current unlock balance": "Current unlock balance",
  "Date of Birth": "Date of Birth",
  "Decimal (maximum 18)": "Decimal (maximum 18)",
  Disconnect: "Disconnect",
  Discord: "Discord",
  Documents: "Documents",
  ERC_1155: "ERC_1155",
  "Edit Profile": "Edit Profile",
  English: "English",
  "Example: No. 1, Nguyen Hue Street": "Example: No. 1, Nguyen Hue Street",
  "External Treasury": "External Treasury",
  "External treasury": "External treasury",
  Facebook: "Facebook",
  "Fail to approve currency": "Fail to approve currency",
  "Fail to transfer NFT": "Fail to transfer NFT",
  "Failed to load estate details": "Failed to load estate details",
  "File(s)": "File(s)",
  ID: "ID",
  IPO: "IPO",
  "Input Wallet Address": "Input Wallet Address",
  "Investor List": "Investor List",
  LEGAL: "LEGAL",
  "Market maker": "Market maker",
  "My NFTs": "My NFTs",
  "My Profile": "My Profile",
  "NFT amount": "NFT amount",
  "No deposits": "No deposits",
  "No estate details found": "No estate details found",
  "No holders": "No holders",
  "No investors": "No investors",
  "No offers": "No offers",
  "No. 1A, Lane 57 Nguyen Khanh Toan, Dich Vong Ward, Cau Giay District, Hanoi City.":
    "No. 1A, Lane 57 Nguyen Khanh Toan, Dich Vong Ward, Cau Giay District, Hanoi City.",
  "Not uploaded": "Not uploaded",
  Offer: "Offer",
  "Please connect your wallet to view your profile details.":
    "Please connect your wallet to view your profile details.",
  "Please upload thumnail": "Please upload thumnail",
  Share: "Share",
  Stage: "Stage",
  "Staking reward": "Staking reward",
  Telegram: "Telegram",
  "Tiếng Việt": "Tiếng Việt",
  Token: "Token",
  Tokenize: "Tokenize",
  Tokens: "Tokens",
  "Total fee": "Total fee",
  "Transfer NFT": "Transfer NFT",
  "Transfer successfully": "Transfer successfully",
  "Transferring Ownership": "Transferring Ownership",
  "Transferring...": "Transferring...",
  "Unknown Chain": "Unknown Chain",
  "Unknown state": "Unknown state",
  "Update contact failed": "Update contact failed",
  "Vesting Until": "Vesting Until",
  "Withdraw All": "Withdraw All",
  "Withdraw NFT failed": "Withdraw NFT failed",
  "Withdraw NFT(s) success": "Withdraw NFT(s) success",
  "Withdraw failed with error": "Withdraw failed with error",
  Withdrawable: "Withdrawable",
  Withdrawn: "Withdrawn",
  X: "X",
  "Your Investments": "Your Investments",
  Zalo: "Zalo",
  brik: "Brik",
  "max selling amount must be less than or equal to total supply":
    "max selling amount must be less than or equal to total supply",
  undefined: "Undefined",
  usdt: "Usdt",
  "Briky Token is the core asset of the Briky Capital ecosystem—representing ownership, utility, and participation in on-chain real estate investment activities.":
    "Briky Token is the core asset of the Briky Capital ecosystem—representing ownership, utility, and participation in on-chain real estate investment activities.",
  "Price based on Chainlink feed at:": "Price based on Chainlink feed at:",
  "Got it": "Got it",
  "USD Denomination": "USD Denomination",
  "Select token": "Select token",
  "Equivalent Value": "Equivalent Value",
  "Decimals must be less than or equal to 18":
    "Decimals must be less than or equal to 18",
}

export default en
