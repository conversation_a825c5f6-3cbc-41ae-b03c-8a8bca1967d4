import React from "react"
import { Image, StyleSheet, View, ViewStyle } from "react-native"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import Colors from "src/config/colors"
import { CustomPressable } from "src/componentsv2"
import { ActivityIndicator } from "react-native-paper"
import defaultAvatar from "assets/imagesV2/ic_avatar.png"

interface AvatarViewProps {
  avatarUrl?: string
  size: number
  enableEdit?: boolean
  style?: ViewStyle
  onEditPress?: () => void
  loading?: boolean
}

const EditButton: React.FC<{
  loading?: boolean
  onPress: () => void
}> = ({ loading, onPress }) => (
  <View style={styles.editIconContainer}>
    {loading ? (
      <ActivityIndicator size="small" color={Colors.primary} />
    ) : (
      <CustomPressable onPress={onPress}>
        <MaterialCommunityIcons
          name="image-edit-outline"
          size={20}
          color={Colors.black7}
        />
      </CustomPressable>
    )}
  </View>
)

const AvatarImage: React.FC<{
  avatarUrl?: string
  size: number
}> = ({ avatarUrl, size }) => {
  const imageSize = avatarUrl ? size : size * 0.75

  return (
    <Image
      source={avatarUrl ? { uri: avatarUrl } : defaultAvatar}
      style={{
        width: imageSize,
        height: imageSize,
        borderRadius: imageSize / 2,
      }}
      tintColor={!avatarUrl ? Colors.black7 : undefined}
    />
  )
}

export const AvatarView: React.FC<AvatarViewProps> = ({
  size,
  enableEdit = false,
  style,
  avatarUrl,
  onEditPress = () => {},
  loading,
}) => {
  return (
    <View
      style={[
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: Colors.black4,
          justifyContent: "center",
          alignItems: "center",
        },
        style,
      ]}
    >
      <AvatarImage avatarUrl={avatarUrl} size={size} />
      {enableEdit && <EditButton loading={loading} onPress={onEditPress} />}
    </View>
  )
}

const styles = StyleSheet.create({
  editIconContainer: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 30,
    height: 30,
    borderRadius: 30,
    backgroundColor: Colors.black4,
    borderWidth: 2,
    borderColor: "white",
    alignItems: "center",
    justifyContent: "center",
  },
})
