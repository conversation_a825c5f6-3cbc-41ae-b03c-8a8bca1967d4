import React, { useState } from "react"
import { FlatList } from "react-native"
import FooterPaging from "./FooterPaging"
import { EmptyView } from "../EmptyView"

interface Props<T> {
  data: T[]
  renderItem: (item: T) => React.ReactElement | null
  keyExtractor?: (item: T, index: number) => string
  scrollEnabled?: boolean
  initialPage?: number
  pageSize?: number
  emptyMessage?: string
}

function SimplePagingLocalList<T>({
  data,
  renderItem,
  keyExtractor,
  scrollEnabled = true,
  initialPage = 1,
  pageSize = 10,
  emptyMessage,
}: Props<T>): React.ReactElement {
  const [currentPage, setCurrentPage] = useState<number>(initialPage)
  const showingList = data.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  const handleNextPage = () => {
    const totalPages = Math.ceil(data.length / pageSize)
    if (currentPage < totalPages) {
      setCurrentPage((prev) => prev + 1)
    }
  }

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1)
    }
  }

  const renderFooter = () => {
    if (data.length === 0) {
      return null
    }
    const totalPages = Math.ceil(data.length / pageSize)
    return (
      <FooterPaging
        itemsOnPage={showingList.length}
        currentPage={currentPage}
        total={data.length}
        totalPages={totalPages}
        onNextPage={handleNextPage}
        onPrevPage={handlePrevPage}
      />
    )
  }

  if (data.length === 0) {
    return <EmptyView subtitle={emptyMessage} />
  }

  return (
    <>
      <FlatList
        data={showingList}
        renderItem={({ item }) => renderItem(item)}
        keyExtractor={keyExtractor}
        scrollEnabled={scrollEnabled}
      />
      {renderFooter()}
    </>
  )
}

export default SimplePagingLocalList
