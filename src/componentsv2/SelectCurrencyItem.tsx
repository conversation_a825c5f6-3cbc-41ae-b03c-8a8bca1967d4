import React from "react"
import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  View,
} from "react-native"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"

interface SelectCurrencyItemProps {
  label: string
  icon: ImageSourcePropType
  selected?: boolean
}

export const SelectCurrencyItem: React.FC<SelectCurrencyItemProps> = ({
  label,
  icon,
  selected,
}) => {
  return (
    <View style={styles.container}>
      <Image source={icon} style={styles.icon} />
      <Text style={[styles.text, selected && styles.selectedText]}>
        {label}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 9,
    marginLeft: 12,
  },
  icon: {
    ...viewStyles.size14Icon,
    marginRight: 4,
  },
  text: {
    ...textStyles.MMedium,
    color: Colors.Neutral300,
  },
  selectedText: {
    color: Colors.PalleteWhite,
  },
})
