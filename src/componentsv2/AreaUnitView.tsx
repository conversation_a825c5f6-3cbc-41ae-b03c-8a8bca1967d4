import React from "react"
import { StyleProp, StyleSheet, Text, TextStyle, View } from "react-native"
import { EstateTokenAreaUnit } from "src/api"
import { formatCurrency } from "src/utils"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"

interface AreaUnitViewProps {
  area: number
  areaUnit: EstateTokenAreaUnit
  textStyle?: StyleProp<TextStyle>
}

const AreaUnitView: React.FC<AreaUnitViewProps> = ({
  area,
  areaUnit,
  textStyle,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.row}>
      {areaUnit === EstateTokenAreaUnit.SQM ? (
        <>
          <Text style={[styles.value, textStyle]}>
            {`${formatCurrency(area)} m`}
          </Text>
          <Text style={[styles.areaUnit, textStyle]}>2</Text>
        </>
      ) : (
        <Text
          style={[styles.value, textStyle]}
        >{`${formatCurrency(area)} ${t("sqft")}`}</Text>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
  },
  value: {
    ...textStyles.XSMedium,
    color: Colors.white,
  },
  areaUnit: {
    ...textStyles.XSMedium,
    color: Colors.white,
  },
})

export { AreaUnitView }
