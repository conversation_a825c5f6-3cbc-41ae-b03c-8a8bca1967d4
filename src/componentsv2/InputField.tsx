import React from "react"
import { StyleSheet, View, ViewStyle, TextInput } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { InputModeOptions } from "react-native/Libraries/Components/TextInput/TextInput"
import { LabelView } from "./LabelView"
import { ErrorLabel } from "./ErrorLabel"

interface InputFieldProps {
  label?: string
  value: string | number
  onChangeText: (text: string | number) => void
  placeholder?: string
  multiline?: boolean
  height?: number
  inputMode?: InputModeOptions
  require?: boolean
  style?: ViewStyle
  onBlur?: () => void
  error?: string
  type?: "string" | "number" | "decimal"
  decimalPlaces?: number
  disabled?: boolean
}

const InputField: React.FC<InputFieldProps> = ({
  label,
  value,
  onChangeText,
  require,
  placeholder,
  multiline = false,
  height = 38,
  inputMode = "text",
  style,
  onBlur,
  error,
  type = "string",
  decimalPlaces = 0,
  disabled = false,
}) => {
  const handleChangeText = (text: string) => {
    const trimmedText = text.trimStart()
    switch (type) {
      case "number": {
        if (!Number.isNaN(Number(trimmedText))) {
          onChangeText(Number(trimmedText))
        }
        break
      }

      case "decimal": {
        const normalizedText = trimmedText.replace(",", ".")

        if (normalizedText === "" || normalizedText === ".") {
          onChangeText(normalizedText)
          break
        }

        if (Number.isNaN(Number(normalizedText))) {
          break
        }

        const parts = normalizedText.split(".")
        if (parts.length > 1 && parts[1].length > decimalPlaces) {
          const limitedDecimal =
            parts[0] + "." + parts[1].substring(0, decimalPlaces)
          onChangeText(limitedDecimal)
        } else {
          onChangeText(normalizedText)
        }
        break
      }

      default: {
        onChangeText(trimmedText)
        break
      }
    }
  }

  return (
    <View style={style}>
      {label && (
        <LabelView label={label} require={require} style={styles.label} />
      )}
      <TextInput
        onBlur={onBlur}
        value={value.toString()}
        onChangeText={handleChangeText}
        placeholder={placeholder}
        placeholderTextColor={Colors.Neutral700}
        multiline={multiline}
        inputMode={inputMode}
        style={[styles.input, { height }, disabled && styles.disabledInput]}
        editable={!disabled}
      />
      <ErrorLabel error={error} />
    </View>
  )
}

const styles = StyleSheet.create({
  label: {
    marginBottom: 4,
  },
  input: {
    height: 38,
    width: "100%",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingHorizontal: 12,
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
  disabledInput: {
    backgroundColor: Colors.Neutral900,
    color: Colors.Neutral300,
  },
})

export { InputField }
