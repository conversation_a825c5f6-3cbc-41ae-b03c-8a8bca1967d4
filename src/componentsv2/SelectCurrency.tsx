import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import React, { useEffect } from "react"
import { StyleSheet, ViewStyle, View, Image } from "react-native"
import { Dropdown } from "react-native-element-dropdown"
import { Currency } from "src/api"
import { formatMoney } from "utils/format"
import { formatEther } from "@ethersproject/units"
import { Control, Controller, UseFormSetValue } from "react-hook-form"
import Logger from "src/utils/logger"
import { LabelView } from "./LabelView"
import { useTranslation } from "react-i18next"
import { SelectCurrencyItem } from "./SelectCurrencyItem"

interface SelectCurrencyProps {
  currencies: Currency[]
  style?: ViewStyle
  inputStyle?: ViewStyle
  require?: boolean
  isShowMinMaxPrice?: boolean
  control: Control<any>
  setValue: UseFormSetValue<any>
}

const logger = new Logger({ tag: "SelectCurrency" })

export const SelectCurrency: React.FC<SelectCurrencyProps> = ({
  currencies,
  control,
  isShowMinMaxPrice = false,
  style,
  inputStyle,
  require = true,
  setValue,
}) => {
  const { t } = useTranslation()

  const deps = currencies.map((i) => i.currency).join()
  useEffect(() => {
    const usdt = currencies.find((i) => i.symbol === "USDT")
    logger.debug("Found USDT currency", usdt)
    if (usdt) {
      logger.debug("Setting currency value", { currency: usdt.currency })
      setValue("currencyId", usdt.currency)
    }
  }, [deps])

  const data = currencies.map((currency) => ({
    label: isShowMinMaxPrice
      ? currency.symbol +
        ` (${formatMoney(formatEther(currency.minUnitPrice))} -> ${formatMoney(
          formatEther(currency.maxUnitPrice)
        )})`
      : currency.symbol,
    value: currency.currency,
    icon: currency.imageUrl ? { uri: currency.imageUrl } : undefined,
  }))

  return (
    <View style={style}>
      <LabelView
        label={t("Select token")}
        require={require}
        style={{ marginBottom: 4 }}
      />
      <Controller
        control={control}
        name="currencyId"
        render={({ field: { onChange, value } }) => (
          <Dropdown
            value={value}
            data={data}
            labelField={"label"}
            valueField={"value"}
            onChange={({ value }) => onChange(value)}
            style={[styles.input, inputStyle]}
            selectedTextStyle={styles.selectedText}
            containerStyle={styles.dropdownContainer}
            activeColor={Colors.Neutral900}
            renderLeftIcon={() => {
              const icon = data.find((i) => i.value === value)?.icon
              return icon ? (
                <Image source={icon} style={styles.icon} resizeMode="contain" />
              ) : null
            }}
            renderItem={(item) => (
              <SelectCurrencyItem
                label={item.label}
                icon={item.icon}
                selected={value === item.value}
              />
            )}
          />
        )}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  input: {
    height: 38,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: "center",
  },
  selectedText: {
    ...textStyles.MMedium,
  },
  dropdownContainer: {
    backgroundColor: Colors.PalleteBlack,
  },
  icon: {
    ...viewStyles.size14Icon,
    marginRight: 4,
  },
})
