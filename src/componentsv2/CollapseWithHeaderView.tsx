import React, { useState } from "react"
import {
  Image,
  ImageSourcePropType,
  Text,
  View,
  ViewStyle,
  StyleSheet,
} from "react-native"
import { SectionHeader } from "./SectionHeader"
import { Divider } from "react-native-paper"
import { textStyles, viewStyles } from "src/config/styles"
import clockImage from "assets/imagesV2/img_clock.png"
import Colors from "src/config/colors"

interface CollapseWithHeaderViewProps {
  title: string
  showEmpty?: boolean
  headerStyle?: ViewStyle
  headerIconUri?: ImageSourcePropType
  children: React.ReactNode
  emptyTitle: string
  style?: ViewStyle
}

const CollapseWithHeaderView: React.FC<CollapseWithHeaderViewProps> = ({
  style,
  headerStyle,
  title,
  showEmpty = false,
  emptyTitle,
  children,
  headerIconUri,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(true)
  return (
    <View style={style}>
      <View style={styles.container}>
        <SectionHeader
          icon={
            headerIconUri && (
              <Image
                source={headerIconUri}
                style={[viewStyles.size20Icon, { marginEnd: 6 }]}
              />
            )
          }
          title={title}
          style={headerStyle}
          isOpen={!isCollapsed}
          onPress={() => setIsCollapsed(!isCollapsed)}
        />
        {!isCollapsed && <Divider />}
        {!isCollapsed &&
          (showEmpty ? <EmptyView emptyTitle={emptyTitle} /> : children)}
      </View>
    </View>
  )
}

const EmptyView: React.FC<{ emptyTitle: string }> = ({ emptyTitle }) => {
  return (
    <View style={{ alignItems: "center", paddingVertical: 16 }}>
      <Image source={clockImage} style={viewStyles.size48Icon} />
      <Text
        style={[
          textStyles.LRegular,
          { marginTop: 16, color: Colors.PalleteWhite },
        ]}
      >
        {emptyTitle}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
  },
})

export { CollapseWithHeaderView }
