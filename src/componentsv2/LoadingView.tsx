import React, { useRef } from "react"
import { Modal, StyleSheet, Text, View, ViewStyle } from "react-native"
import Lottie<PERSON>ie<PERSON> from "lottie-react-native"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import loadingAnimation from "assets/lottie/ani_loading.json"

interface LoadingViewProps {
  containerStyle?: ViewStyle
  size?: number
}

const LoadingView: React.FC<LoadingViewProps> = ({
  containerStyle,
  size = 60,
}) => {
  const { t } = useTranslation()
  const animation = useRef<LottieView>(null)

  return (
    <Modal visible={true} transparent={true}>
      <View style={[styles.container, containerStyle]}>
        <View style={styles.loadingContainer}>
          <LottieView
            autoPlay
            ref={animation}
            style={[styles.lottie, { width: size, height: size }]}
            source={loadingAnimation}
          />
          <View
            style={{
              width: "100%",
              height: 10,
              marginTop: -10,
              backgroundColor: "#3a3e4e",
            }}
          ></View>
          <Text style={[textStyles.bodyM, styles.loadingText]}>
            {t("Loading...")}
          </Text>
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    width: "100%",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  loadingContainer: {
    backgroundColor: "#3a3e4e",
    paddingVertical: 32,
    borderRadius: 12,
    width: 130,
    alignItems: "center",
  },
  lottie: {
    width: 60,
    height: 60,
  },
  loadingText: {
    color: Colors.white,
  },
})

export { LoadingView }
