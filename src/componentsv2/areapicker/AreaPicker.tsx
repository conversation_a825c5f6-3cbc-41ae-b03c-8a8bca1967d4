import React from "react"
import { StyleSheet, View, ViewStyle } from "react-native"
import vietnamLocation from "./vietnam.json"
import { useTranslation } from "react-i18next"
import { LabelView } from "src/componentsv2/LabelView"
import { Dropdown } from "react-native-element-dropdown"
import { ErrorLabel } from "src/componentsv2/ErrorLabel"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import DropdownRenderItem from "../DropdownRenderItem"

type Props = {
  invalidAddressCodeLevel1?: boolean
  invalidAddressCodeLevel2?: boolean
  invalidAddressCodeLevel3?: boolean
  addressCodeLevel1: string
  addressCodeLevel2: string
  addressCodeLevel3: string
  errorMessage?: string
  onChange: (level: string, value: string) => void
  style?: ViewStyle
}

const vietnam = vietnamLocation as Record<string, Record<string, string[]>>

export const AreaPicker: React.FunctionComponent<Props> = ({
  addressCodeLevel1,
  addressCodeLevel2,
  addressCodeLevel3,
  errorMessage,
  onChange,
  style,
}) => {
  const { t } = useTranslation()
  return (
    <>
      <View style={style}>
        <LabelView
          label={t("Select area")}
          require={true}
          textStyle={styles.areaLabel}
        />
        <Dropdown
          data={Object.keys(vietnam)
            .sort()
            .map((province) => ({
              label: province,
              value: province,
            }))}
          labelField="label"
          valueField="value"
          value={addressCodeLevel1}
          onChange={({ value }) => onChange("addressCodeLevel1", value)}
          placeholder={t("City")}
          style={styles.input}
          selectedTextStyle={styles.dropdownItem}
          placeholderStyle={styles.dropdownItem}
          itemTextStyle={styles.dropdownItem}
          renderItem={DropdownRenderItem}
        />
        {addressCodeLevel1 && (
          <Dropdown
            data={Object.keys(vietnam[addressCodeLevel1]).map((district) => ({
              label: district,
              value: district,
            }))}
            labelField="label"
            valueField="value"
            selectedTextStyle={styles.dropdownItem}
            placeholderStyle={styles.dropdownItem}
            itemTextStyle={styles.dropdownItem}
            renderItem={DropdownRenderItem}
            style={styles.input}
            value={addressCodeLevel2}
            onChange={({ value }) => onChange("addressCodeLevel2", value)}
            placeholder={t("District")}
          />
        )}
        {addressCodeLevel2 && (
          <Dropdown
            data={vietnam[addressCodeLevel1][addressCodeLevel2].map((ward) => ({
              label: ward,
              value: ward,
            }))}
            labelField="label"
            valueField="value"
            selectedTextStyle={styles.dropdownItem}
            placeholderStyle={styles.dropdownItem}
            itemTextStyle={styles.dropdownItem}
            renderItem={DropdownRenderItem}
            style={styles.input}
            value={addressCodeLevel3}
            onChange={({ value }) => onChange("addressCodeLevel3", value)}
            placeholder={t("Ward")}
          />
        )}
      </View>
      {errorMessage && <ErrorLabel error={errorMessage} />}
    </>
  )
}

const styles = StyleSheet.create({
  areaLabel: {
    marginBottom: 6,
  },
  input: {
    height: 32,
    width: "100%",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    padding: 12,
    alignItems: "center",
    marginBottom: 6,
  },
  dropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
  renderDropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 12,
  },
})
