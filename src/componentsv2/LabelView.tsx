import React from "react"
import { StyleSheet, Text, TextStyle, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"

interface LabelViewProps {
  label: string
  require?: boolean
  style?: ViewStyle
  textStyle?: TextStyle
}

export const LabelView: React.FC<LabelViewProps> = ({
  label,
  require,
  style,
  textStyle,
}) => {
  return (
    <View style={[style, styles.container]}>
      <Text style={[styles.label, textStyle]}>{label}</Text>
      {require && (
        <Text style={[styles.label, textStyle, styles.require]}>*</Text>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
  },
  label: {
    ...textStyles.MMedium,
    color: Colors.Neutral500,
  },
  require: {
    color: Colors.Danger500,
    marginStart: 4,
  },
})
