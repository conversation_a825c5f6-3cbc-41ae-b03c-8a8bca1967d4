import React from "react"
import { StyleSheet, Text, ViewStyle } from "react-native"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"

interface ErrorLabelProps {
  error?: string
  style?: ViewStyle
}

const ErrorLabel: React.FC<ErrorLabelProps> = ({ error, style }) => {
  if (!error) return null

  return <Text style={[styles.error, style]}>{error}</Text>
}

const styles = StyleSheet.create({
  error: {
    ...textStyles.SMedium,
    marginTop: 4,
    color: Colors.Danger500,
  },
})

export { ErrorLabel }
