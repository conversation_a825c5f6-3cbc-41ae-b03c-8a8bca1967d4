import React, { ReactNode } from "react"
import { StyleSheet, View } from "react-native"
import Colors from "src/config/colors"

const Background: React.FC<{
  children: ReactNode
}> = ({ children }) => {
  return <View style={styles.background}>{children}</View>
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
    backgroundColor: Colors.PalleteBlack,
  },
})

export { Background }
