import React from "react"
import { Image, StyleSheet, Text, View, ViewStyle } from "react-native"
import { useTranslation } from "react-i18next"
import * as Clipboard from "expo-clipboard"

import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { shortenAddress } from "utils/stringExt"
import { showSuccess } from "utils/toast"
import { CustomPressable } from "src/componentsv2"
import copyIcon from "assets/imagesV2/ic_copy.png"

interface AddressViewProps {
  address?: string
  isShowFullAddress?: boolean
  style?: ViewStyle
  copy?: boolean
}

export const AddressView: React.FC<AddressViewProps> = ({
  address,
  style,
  isShowFullAddress = false,
  copy = true,
}) => {
  const { t } = useTranslation()

  const handleCopy = async () => {
    if (!address) return
    await Clipboard.setStringAsync(address)
    showSuccess(t("Address copied"))
  }

  const displayAddress = isShowFullAddress
    ? address
    : address != null
      ? shortenAddress(address)
      : t("undefined")

  return (
    <CustomPressable style={style} onPress={handleCopy}>
      <View style={styles.container}>
        <Text style={styles.addressText}>{displayAddress}</Text>
        {address && copy && (
          <Image
            source={copyIcon}
            style={styles.copyIcon}
            tintColor={Colors.Neutral300}
          />
        )}
      </View>
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: "auto",
  },
  addressText: {
    ...textStyles.SMedium,
    color: Colors.white,
  },
  copyIcon: {
    ...viewStyles.size12Icon,
    marginStart: 8,
  },
})
