import React, { useCallback } from "react"
import {
  getImageType,
  getPhotoFileName,
  useChoosePhoto,
} from "utils/choosePhotoExt"
import { Image, ScrollView, StyleSheet, View, Text } from "react-native"
import { CustomPressable } from "src/componentsv2"
import Colors from "src/config/colors"
import uploadIcon from "assets/imagesV2/ic_upload.png"
import icClose from "assets/imagesV2/ic_close.png"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"

interface ImageData {
  url: string
  fileName: string
  type: string
}

interface UploadImagesProps {
  images: ImageData[]
  isHideAddImageIcon?: boolean
  onChangeImages: (images: ImageData[]) => void
  allowsMultipleSelection?: boolean
}

interface ImageItemProps {
  image: ImageData
  onRemove: () => void
}

const ImageItem: React.FC<ImageItemProps> = ({ image, onRemove }) => (
  <View key={image.url} style={styles.imageWrapper}>
    <Image
      source={{ uri: image.url }}
      style={styles.image}
      resizeMode="cover"
    />
    <CustomPressable onPress={onRemove} style={styles.removeButton}>
      <Image source={icClose} style={viewStyles.size16Icon} />
    </CustomPressable>
  </View>
)

const AddImageButton: React.FC<{ onPress: () => void }> = ({ onPress }) => {
  const { t } = useTranslation()
  return (
    <CustomPressable onPress={onPress} style={styles.addButton}>
      <Image
        source={uploadIcon}
        style={[viewStyles.size20Icon, { tintColor: Colors.PalleteWhite }]}
      />
      <Text style={styles.textUpload}>{t("Upload file")}</Text>
    </CustomPressable>
  )
}

const UploadImages: React.FC<UploadImagesProps> = ({
  images,
  onChangeImages,
  isHideAddImageIcon,
  allowsMultipleSelection = true,
}) => {
  const { handleChoosePhoto } = useChoosePhoto()

  const pickImage = useCallback(() => {
    handleChoosePhoto(
      (results) => {
        const newImages = results.map((result) => {
          const fileName = getPhotoFileName(result.fileName || null, result.uri)
          const type = getImageType(fileName)
          return {
            url: result.uri,
            fileName,
            type,
          }
        })
        onChangeImages([...images, ...newImages])
      },
      {
        mediaTypes: ["images"],
        allowsEditing: false,
        quality: 1,
        allowsMultipleSelection: allowsMultipleSelection,
      }
    )
  }, [
    images,
    handleChoosePhoto,
    onChangeImages,
    getImageType,
    getPhotoFileName,
  ])

  const removeImage = useCallback(
    (imageToRemove: ImageData) => {
      onChangeImages(images.filter((img) => img !== imageToRemove))
    },
    [images, onChangeImages]
  )

  return (
    <View style={styles.container}>
      {!isHideAddImageIcon && <AddImageButton onPress={pickImage} />}
      <ScrollView horizontal>
        <View style={styles.imagesContainer}>
          {images.map((image) => (
            <ImageItem
              key={image.url}
              image={image}
              onRemove={() => removeImage(image)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.Neutral800,
    borderRadius: 4,
  },
  addButton: {
    width: 80,
    height: 80,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: "dashed",
    borderColor: Colors.Neutral800,
    margin: 16,
    gap: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  textUpload: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
  imagesContainer: {
    flexDirection: "row",
  },
  imageWrapper: {
    width: 80,
    height: 80,
    marginHorizontal: 4,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.Neutral800,
  },
  removeButton: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: Colors.PalleteWhite,
    borderRadius: 999,
  },
})

export { UploadImages }
