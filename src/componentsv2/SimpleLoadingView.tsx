import React from "react"
import { Modal, StyleSheet, Text, View } from "react-native"
import { ActivityIndicator } from "react-native-paper"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { CustomPressable } from "./CustomPressable"

interface SimpleLoadingViewProps {
  visible: boolean
  onCancel?: () => void
}

const SimpleLoadingView: React.FC<SimpleLoadingViewProps> = ({
  visible,
  onCancel,
}) => {
  const { t } = useTranslation()

  return (
    <Modal visible={visible} transparent={true}>
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.title}>{t("Loading...")}</Text>
          <ActivityIndicator size="large" color={Colors.primary} />
          {onCancel && (
            <CustomPressable onPress={onCancel} style={styles.cancelButton}>
              <Text style={styles.cancelText}>{t("Cancel")}</Text>
            </CustomPressable>
          )}
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    backgroundColor: Colors.opacityBlack60,
    flex: 1,
    padding: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    width: "95%",
    position: "relative",
    backgroundColor: Colors.PalleteBlack,
    padding: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    ...textStyles.LMedium,
    marginBottom: 8,
    color: Colors.PalleteWhite,
  },
  cancelButton: {
    padding: 8,
  },
  cancelText: {
    ...textStyles.LMedium,
    color: Colors.blueLink,
  },
})

export { SimpleLoadingView }
