import "@walletconnect/react-native-compat"
import "src/config/polyfills"
import React, { useEffect, useCallback } from "react"
import Navigator from "src/navigatorV2"
import { useFonts } from "expo-font"
import * as SplashScreen from "expo-splash-screen"
import "locales/i18n"
import { StatusBar, View } from "react-native"
import {
  SafeAreaProvider,
  useSafeAreaInsets,
} from "react-native-safe-area-context"
import { QueryClientProvider } from "@tanstack/react-query"
import { AuthProvider, profileAtom } from "src/context/AuthContext"
import {
  activeChain,
  WalletConnectProvider,
} from "src/provider/walletconnect/WalletConnectProvider"
import { useLogin } from "src/hooks/useLogin"
import { ActionSheetProvider } from "@expo/react-native-action-sheet"
import { AppKit, createAppKit } from "@reown/appkit-wagmi-react-native"
import { WALLET_CONNECT_PROJECT_ID } from "src/config/env"
import { wagmiConfig } from "src/provider/walletconnect/WalletConnectProvider"
import * as Clipboard from "expo-clipboard"
import { queryClient } from "src/api/query"
import { initializeLogger } from "src/utils/loggerConfig"

// Import fonts
import Allerta from "assets/fonts/Allerta-Regular.ttf"
import Audiowide from "assets/fonts/Audiowide-Regular.ttf"
import Arimo from "assets/fonts/Arimo-VariableFont_wght.ttf"
import RobotoBold from "assets/fonts/Roboto-Bold.ttf"
import RobotoLight from "assets/fonts/Roboto-Light.ttf"
import RobotoRegular from "assets/fonts/Roboto-Regular.ttf"
import RobotoMedium from "assets/fonts/Roboto-Medium.ttf"
import InterBold from "assets/fonts/Inter-Bold.ttf"
import InterMedium from "assets/fonts/Inter-Medium.ttf"
import InterRegular from "assets/fonts/Inter-Regular.ttf"
import InterSemiBold from "assets/fonts/Inter-SemiBold.ttf"
import { siweConfig } from "src/siweConfig"
import { useAtom } from "jotai"
import Colors from "./src/config/colors"

SplashScreen.preventAutoHideAsync()

const App: React.FC = () => {
  const [loaded, error] = useFonts({
    Allerta,
    Audiowide,
    Arimo,
    RobotoBold,
    RobotoLight,
    RobotoRegular,
    RobotoMedium,
    InterBold,
    InterMedium,
    InterRegular,
    InterSemiBold,
  })

  const [isFetchData, setIsFetchData] = React.useState(false)
  const [, setProfile] = useAtom(profileAtom)
  const { prepare } = useLogin()

  useEffect(() => {
    // Initialize logger early in the app lifecycle
    initializeLogger()

    const prepareApp = async () => {
      try {
        await prepare(true)
      } finally {
        setIsFetchData(true)
      }
    }
    prepareApp()
  }, [])

  useEffect(() => {
    createAppKit({
      projectId: WALLET_CONNECT_PROJECT_ID,
      wagmiConfig,
      defaultChain: activeChain,
      enableAnalytics: false,
      clipboardClient: {
        setString: async (value: string) => {
          await Clipboard.setStringAsync(value)
        },
      },
      includeWalletIds: [
        "c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96", // MetaMask
      ],
      siweConfig: siweConfig((profile) => {
        setProfile(profile)
      }),
    })
  }, [])

  const isAppReady = (loaded || error) && isFetchData

  const onLayoutRootView = useCallback(async () => {
    if (isAppReady) {
      await SplashScreen.hideAsync()
    }
  }, [isAppReady])

  if (!isAppReady) {
    return null
  }

  return (
    <WalletConnectProvider>
      <QueryClientProvider client={queryClient}>
        <View style={{ flex: 1 }}>
          <StatusBar
            animated={true}
            backgroundColor={Colors.blackNew}
            barStyle={"light-content"}
          />
          <SafeAreaProvider onLayout={onLayoutRootView}>
            <SafeAreaContent>
              <ActionSheetProvider>
                <AuthProvider>
                  <>
                    <Navigator />
                    <AppKit />
                  </>
                </AuthProvider>
              </ActionSheetProvider>
            </SafeAreaContent>
          </SafeAreaProvider>
        </View>
      </QueryClientProvider>
    </WalletConnectProvider>
  )
}

const SafeAreaContent: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const insets = useSafeAreaInsets()
  return (
    <View
      style={{
        flex: 1,
        paddingTop: insets.top,
        paddingBottom: insets.bottom,
        backgroundColor: Colors.Black,
      }}
    >
      {children}
    </View>
  )
}

export default App
