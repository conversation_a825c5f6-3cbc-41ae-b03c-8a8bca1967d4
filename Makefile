lint-check:
	yarn lint

lint-fix:
	yarn lint:fix

format:
	yarn format

check-all:
	make format
	make lint-fix
	npx tsc -noEmit

run:
	cp .env.staging.sample .env
	npx expo start

run-uat:
	cp .env.uat.sample .env
	npx expo start

run-prod:
	cp .env.production.sample .env
	npx expo start

build-staging:
	cp .env.staging.sample .env
	eas build --platform android --profile staging

build-uat:
	cp .env.uat.sample .env
	eas build --platform android --profile uat

release-internal:
	cp .env.staging.sample .env
	eas submit --platform android --profile internal --path ./android/app/build/outputs/bundle/debug/app-debug.aab

release-staging:
	cp .env.staging.sample .env
	eas build --platform android --profile staging-release

release-uat:
	cp .env.uat.sample .env
	eas build --platform android --profile uat-release

production:
	cp .env.production.sample .env
	eas build --platform android --profile production

release:
	cp .env.production.sample .env
	eas build --platform android --profile release
