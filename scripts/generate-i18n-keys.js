#!/usr/bin/env node

const fs = require("fs")
const path = require("path")

// Đọc file en.ts hiện tại
function readEnFile() {
  const enFilePath = path.join(__dirname, "../src/locales/en.ts")
  try {
    const content = fs.readFileSync(enFilePath, "utf8")
    // Parse nội dung để lấy các key hiện có
    const existingKeys = new Set()

    // Regex để tìm các key trong object
    const keyRegex = /^\s*["']?([^"':]+)["']?\s*:/gm
    let match

    while ((match = keyRegex.exec(content)) !== null) {
      const key = match[1].trim()
      if (key && key !== "const en") {
        existingKeys.add(key)
      }
    }

    return { content, existingKeys }
  } catch (error) {
    console.error("Lỗi khi đọc file en.ts:", error)
    return { content: "", existingKeys: new Set() }
  }
}

// Tìm tất cả file .tsx và .ts trong thư mục src
function findSourceFiles(dir) {
  const files = []

  function scanDirectory(currentDir) {
    try {
      const items = fs.readdirSync(currentDir)

      for (const item of items) {
        const fullPath = path.join(currentDir, item)
        const stat = fs.statSync(fullPath)

        if (stat.isDirectory()) {
          // Bỏ qua node_modules và các thư mục ẩn
          if (!item.startsWith(".") && item !== "node_modules") {
            scanDirectory(fullPath)
          }
        } else if (
          stat.isFile() &&
          (item.endsWith(".tsx") || item.endsWith(".ts"))
        ) {
          files.push(fullPath)
        }
      }
    } catch (error) {
      console.error(`Lỗi khi quét thư mục ${currentDir}:`, error)
    }
  }

  scanDirectory(dir)
  return files
}

// Tìm các key được sử dụng trong t() function
function findTranslationKeys(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8")
    const keys = new Set()

    // Kiểm tra xem file có import useTranslation từ react-i18next không
    const hasI18nImport =
      /import.*useTranslation.*from.*["']react-i18next["']/.test(content)
    if (!hasI18nImport) {
      return keys
    }

    // Regex để tìm các pattern t("key") hoặc t('key')
    const tFunctionRegex = /\bt\s*\(\s*["']([^"']+)["']\s*\)/g
    let match

    while ((match = tFunctionRegex.exec(content)) !== null) {
      const key = match[1]
      if (key) {
        keys.add(key)
      }
    }

    return keys
  } catch (error) {
    console.error(`Lỗi khi đọc file ${filePath}:`, error)
    return new Set()
  }
}

// Tạo entry mới cho key
function createKeyEntry(key) {
  // Tạo value mặc định bằng cách format key
  let defaultValue = key

  // Nếu key có dạng camelCase, chuyển thành readable text
  if (/^[a-z][a-zA-Z0-9]*$/.test(key)) {
    defaultValue = key
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase())
      .trim()
  }

  return `  "${key}": "${defaultValue}",`
}

// Cập nhật file en.ts
function updateEnFile(newKeys) {
  if (newKeys.size === 0) {
    console.log("Không có key mới nào cần thêm.")
    return
  }

  const enFilePath = path.join(__dirname, "../src/locales/en.ts")
  const { content } = readEnFile()

  // Tìm vị trí cuối của object (trước dấu })
  const lastBraceIndex = content.lastIndexOf("}")

  if (lastBraceIndex === -1) {
    console.error("Không thể tìm thấy cấu trúc object trong file en.ts")
    return
  }

  // Tạo nội dung mới để thêm vào
  const newEntries = Array.from(newKeys)
    .sort()
    .map((key) => createKeyEntry(key))
    .join("\n")

  // Chèn các key mới vào trước dấu }
  const beforeBrace = content.substring(0, lastBraceIndex)
  const afterBrace = content.substring(lastBraceIndex)

  // Kiểm tra xem có dấu phẩy cuối không
  const needsComma = !beforeBrace.trim().endsWith(",")
  const comma = needsComma ? "," : ""

  const newContent = beforeBrace + comma + "\n" + newEntries + "\n" + afterBrace

  // Ghi file
  try {
    fs.writeFileSync(enFilePath, newContent, "utf8")
    console.log(`Đã thêm ${newKeys.size} key mới vào file en.ts:`)
    newKeys.forEach((key) => console.log(`  - ${key}`))
  } catch (error) {
    console.error("Lỗi khi ghi file en.ts:", error)
  }
}

// Main function
function main() {
  console.log("Bắt đầu quét các file để tìm translation keys...")

  // Đọc file en.ts hiện tại
  const { existingKeys } = readEnFile()
  console.log(`Tìm thấy ${existingKeys.size} key hiện có trong en.ts`)

  // Tìm tất cả file source
  const srcDir = path.join(__dirname, "../src")
  const sourceFiles = findSourceFiles(srcDir)
  console.log(`Tìm thấy ${sourceFiles.length} file source để quét`)

  // Tìm tất cả key được sử dụng
  const usedKeys = new Set()
  let filesWithI18n = 0

  for (const filePath of sourceFiles) {
    const keys = findTranslationKeys(filePath)
    if (keys.size > 0) {
      filesWithI18n++
      keys.forEach((key) => usedKeys.add(key))
    }
  }

  console.log(`Tìm thấy ${filesWithI18n} file sử dụng react-i18next`)
  console.log(`Tổng cộng ${usedKeys.size} key được sử dụng trong code`)

  // Tìm key mới (chưa có trong en.ts)
  const newKeys = new Set()
  for (const key of usedKeys) {
    if (!existingKeys.has(key)) {
      newKeys.add(key)
    }
  }

  console.log(`Tìm thấy ${newKeys.size} key mới cần thêm`)

  // Cập nhật file en.ts
  updateEnFile(newKeys)

  console.log("Hoàn thành!")
}

// Chạy script
if (require.main === module) {
  main()
}

module.exports = {
  readEnFile,
  findSourceFiles,
  findTranslationKeys,
  updateEnFile,
}
