name: Build internal Android bundle

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Java JDK
        uses: actions/setup-java@v3
        with:
          distribution: "temurin"
          java-version: "17"

      - name: Install Node.js v20 and NPM
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Install project dependencies
        run: npm install --legacy-peer-deps

      - name: Install Expo CLI and dependencies (if Expo project)
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install EAS CLI globally
        run: npm install -g eas-cli

      - name: Prebuild Expo project for Android
        run: |
          cp .env.staging.sample .env
          npx expo prebuild --platform android --clean
        continue-on-error: true

      - name: Set up Android SDK
        uses: android-actions/setup-android@v3

      - name: Build Android bundle
        working-directory: android
        run: ./gradlew clean app:bundleRelease

      - name: Upload to Google Play
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.brikyland.app
          releaseFiles: android/app/build/outputs/bundle/release/app-release.aab
          track: internalsharing
          status: inProgress
          inAppUpdatePriority: 2
          userFraction: 0.33
